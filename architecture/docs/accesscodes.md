Below is the **drop-in swap-out** for Convex. Everything else in your stack (Next 14/15, <PERSON><PERSON><PERSON><PERSON> Router, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, AISDK, OpenRouter models) stays the same—the only thing we change is the storage/validation layer.

---

## 1 · Create a Convex table for the codes

`convex/schema.ts`

```ts
import { defineSchema, defineTable, v } from "convex/server";

export default defineSchema({
  accessCodes: defineTable({
    codeHash:   v.string(),        // bcrypt hash, NEVER plain text
    pathPrefix: v.string(),        // e.g. "/private/" or "/reports/q2/"
    expiresAt:  v.optional(v.int64()),
    usedOnce:   v.optional(v.boolean()),
    createdAt:  v.optional(v.int64()),
  }).index("by_path", ["pathPrefix"]),
});
```

> **Wildcard strategy:**
> Convex doesn’t have SQL-style `LIKE`, so store *prefixes* (`/private/`) or *exact paths*.
> If you need richer matching, save a `RegExp` string and run `new RegExp(row.pattern).test(pathname)` in the query.

---

## 2 · Convex functions

### `convex/functions/accessCodes.ts`

```ts
import { query, mutation } from "./_generated/server";
import { v, ConvexError } from "convex/values";
import bcrypt from "bcryptjs";

/** Validate a user-supplied code for a given path */
export const validateAccessCode = query({
  args: {
    rawCode: v.string(),
    pathname: v.string(),
  },
  handler: async (ctx, { rawCode, pathname }) => {
    const rows = await ctx.db
      .query("accessCodes")
      .withIndex("by_path", q => q.eq("pathPrefix", pathname.startsWith("/") ? pathname : `/${pathname}`))
      .collect();          // normally small—1–3 rows

    for (const row of rows) {
      const ok =
        (await bcrypt.compare(rawCode, row.codeHash)) &&
        (!row.expiresAt || row.expiresAt > Date.now()) &&
        !row.usedOnce;

      if (ok) {
        if (row.usedOnce) await ctx.db.patch(row._id, { usedOnce: true });
        return true;
      }
    }
    return false;
  },
});

/** Helper to seed / rotate codes from the dashboard or a CLI */
export const createAccessCode = mutation({
  args: {
    rawCode:   v.string(),
    pathPrefix:v.string(),
    expiresAt: v.optional(v.int64()),
    singleUse: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new ConvexError("Unauthenticated");   // or restrict to admin

    const codeHash = await bcrypt.hash(args.rawCode, 10);
    await ctx.db.insert("accessCodes", {
      codeHash,
      pathPrefix: args.pathPrefix,
      expiresAt: args.expiresAt,
      usedOnce:   args.singleUse ?? false,
      createdAt:  Date.now(),
    });
  },
});
```

*You can run `npx convex run createAccessCode` from the CLI to seed codes quickly.*

---

## 3 · Server helper (Next API route) that calls Convex

`app/api/verify-code/route.ts`  (Edge **off** – keep it in the Node runtime)

```ts
import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";
import { fetchQuery } from "convex/nextjs";          // helper for Node & App-router
import { api } from "@/convex/_generated/api";

export async function POST(req: NextRequest) {
  const { code, pathname } = (await req.json()) as {
    code: string;
    pathname: string;
  };

  const ok = await fetchQuery(api.accessCodes.validateAccessCode, {
    rawCode: code,
    pathname,
  });                         // Convex over HTTPS – no socket needed :contentReference[oaicite:0]{index=0}

  if (!ok) return NextResponse.json({ ok: false }, { status: 401 });

  // Seal in an http-only cookie (JWT/HMAC if you prefer)
  cookies().set({
    name: "access_code",
    value: code,
    httpOnly: true,
    maxAge: 60 * 60 * 24,      // 24 h
    sameSite: "lax",
    secure: process.env.NODE_ENV === "production",
    path: "/",
  });

  return NextResponse.json({ ok: true });
}
```

---

## 4 · Edge middleware (unchanged)

Because Convex queries can’t run in an Edge runtime, keep the middleware the same as the Supabase version: read the cookie early and skip the network round-trip. If you want *extra* safety, encode the cookie as a short-lived JWT with `jose` and verify it in the Edge function (zero DB hits).

---

## 5 · Unlock page (client) and TanStack Router

No change required. Your earlier `<UnlockPage>` still posts to `/api/verify-code` and, on success, navigates back.

---

## 6 · Why the multi-layer check still matters with Convex

Convex exposes its functions as a public API, so **backend logic must enforce the same rules that your middleware does** (e.g. checking `ctx.auth`)—otherwise a bad actor could call the query directly. Keep the `validateAccessCode` query **public**, but make sure the *data* behind each protected page isn’t exposed by other queries without equivalent checks. ([stack.convex.dev][1])

---

## 7 · Optional: store the flag in Clerk instead of a cookie

If every visitor is already signed in:

```ts
import { clerkClient } from "@clerk/nextjs/server";
// after ok === true
await clerkClient.users.updateUserMetadata(clerkUserId, {
  privateMetadata: { [`access:${pathname}`]: true },
});
```

Then your middleware can read `auth().session?.privateMetadata`.

---

## 8 · Quick-and-dirty proto without Convex

Set `ACCESS_CODES` in `.env` and replace `fetchQuery(...)` with a constant lookup (exactly as in the Supabase answer).

---

### TL;DR

*Switching from Supabase to Convex is a one-file change:*

* Define `accessCodes` in `convex/schema.ts`.
* Expose a `validateAccessCode` query.
* In `/api/verify-code` call it with `fetchQuery(...)`.

The rest—Edge middleware, TanStack Router flow, Clerk integration—stays the same. Ship it!

[1]: https://stack.convex.dev/authentication-best-practices-convex-clerk-and-nextjs "Authentication Best Practices: Convex, Clerk and Next.js"
