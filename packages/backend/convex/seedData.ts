import { mutation } from "./_generated/server";
import { v } from "convex/values";

// Seed initial social accounts data
export const seedSocialAccounts = mutation({
  handler: async (ctx) => {
    // Check if data already exists
    const existingAccounts = await ctx.db.query("socialAccounts").collect();
    if (existingAccounts.length > 0) {
      return "Data already exists";
    }

    // Create social accounts with real tweet URLs
    const accounts = [
      {
        handle: "@MarioNawfal",
        displayName: "Mario Nawfal",
        platform: "X (Twitter)",
        tweetUrl: "https://x.com/MarioNawfal/status/1931690855712051345",
        monthlyImpressions: 2500000,
        monthlySpaceListeners: 85000,
        displayOrder: 1,
        isActive: true,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      },
      {
        handle: "@RoundtableSpace",
        displayName: "Roundtable",
        platform: "X Spaces",
        tweetUrl: "https://x.com/RoundtableSpace/status/1897808011361493083",
        monthlyImpressions: 1800000,
        monthlySpaceListeners: 45000,
        displayOrder: 2,
        isActive: true,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      },
      {
        handle: "@Crypto_TownHall",
        displayName: "Crypto Town Hall",
        platform: "X Spaces",
        tweetUrl: "https://x.com/Crypto_TownHall/status/1810317001396568325",
        monthlyImpressions: 1200000,
        monthlySpaceListeners: 32000,
        displayOrder: 3,
        isActive: true,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      },
    ];

    const insertedIds = [];
    for (const account of accounts) {
      const id = await ctx.db.insert("socialAccounts", account);
      insertedIds.push(id);
    }

    return `Created ${insertedIds.length} social accounts`;
  },
});

// Seed case studies data
export const seedCaseStudies = mutation({
  handler: async (ctx) => {
    // Check if data already exists
    const existingCaseStudies = await ctx.db.query("caseStudies").collect();
    if (existingCaseStudies.length > 0) {
      return "Case studies already exist";
    }

    // Helper function to calculate performance score
    function calculatePerformanceScore(metrics: any): number {
      const { views = 0, likes = 0, retweets = 0, engagement = 0, impressions = 0 } = metrics;
      
      const engagementRate = impressions > 0 ? (likes + retweets) / impressions : 0;
      const viralScore = retweets > 0 ? likes / retweets : likes;
      const reachScore = Math.log10(Math.max(1, views || impressions));
      
      return Math.round((engagementRate * 1000) + (viralScore * 10) + (reachScore * 100));
    }

    // Helper function to generate searchable content
    function generateSearchableContent(title: string, description: string, tags: string[] = [], accountHandle?: string): string {
      const handleText = accountHandle ? accountHandle.replace('@', '') : '';
      return [title, description, ...tags, handleText].filter(Boolean).join(' ').toLowerCase();
    }

    const caseStudies = [
      {
        title: "Viral Crypto Campaign",
        category: "media" as const,
        description: "Launched a multi-platform campaign that reached 50M+ users and generated massive engagement across Twitter, YouTube, and TikTok.",
        tags: ["crypto", "viral", "multi-platform", "engagement"],
        metrics: {
          impressions: ********,
          engagement: 8.5,
          conversions: 125000,
        },
        searchableContent: generateSearchableContent(
          "Viral Crypto Campaign",
          "Launched a multi-platform campaign that reached 50M+ users and generated massive engagement across Twitter, YouTube, and TikTok.",
          ["crypto", "viral", "multi-platform", "engagement"]
        ),
        featured: true,
        performanceScore: calculatePerformanceScore({
          impressions: ********,
          engagement: 8.5,
          conversions: 125000,
        }),
        isActive: true,
        displayOrder: 1,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      },
      {
        title: "DeFi Protocol Launch",
        category: "marketing" as const,
        description: "Strategic marketing campaign for a new DeFi protocol that achieved $100M TVL within the first month.",
        tags: ["defi", "protocol", "tvl", "marketing"],
        metrics: {
          impressions: 25000000,
          engagement: 12.3,
          conversions: 85000,
        },
        searchableContent: generateSearchableContent(
          "DeFi Protocol Launch",
          "Strategic marketing campaign for a new DeFi protocol that achieved $100M TVL within the first month.",
          ["defi", "protocol", "tvl", "marketing"]
        ),
        featured: false,
        performanceScore: calculatePerformanceScore({
          impressions: 25000000,
          engagement: 12.3,
          conversions: 85000,
        }),
        isActive: true,
        displayOrder: 2,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      },
    ];

    const insertedIds = [];
    for (const caseStudy of caseStudies) {
      const id = await ctx.db.insert("caseStudies", caseStudy);
      insertedIds.push(id);
    }

    return `Created ${insertedIds.length} case studies`;
  },
});

// Seed Elon interactions
export const seedElonInteractions = mutation({
  handler: async (ctx) => {
    // Check if data already exists
    const existingInteractions = await ctx.db.query("elonInteractions").collect();
    if (existingInteractions.length > 0) {
      return "Elon interactions already exist";
    }

    const interactions = [
      {
        type: "retweet" as const,
        tweetId: "1931690855712051345",
        content: "This is exactly what crypto needs right now. Great work!",
        timestamp: Date.now() - ********, // 1 day ago
        sourceAccount: "@MarioNawfal",
        isActive: true,
      },
      {
        type: "mention" as const,
        tweetId: "1897808011361493083",
        content: "Impressive analysis on the current market conditions.",
        timestamp: Date.now() - *********, // 2 days ago
        sourceAccount: "@RoundtableSpace",
        isActive: true,
      },
      {
        type: "reply" as const,
        tweetId: "1810317001396568325",
        content: "Looking forward to more discussions like this.",
        timestamp: Date.now() - *********, // 3 days ago
        sourceAccount: "@Crypto_TownHall",
        isActive: true,
      },
    ];

    const insertedIds = [];
    for (const interaction of interactions) {
      const id = await ctx.db.insert("elonInteractions", interaction);
      insertedIds.push(id);
    }

    return `Created ${insertedIds.length} Elon interactions`;
  },
});

// Seed homepage configuration
export const seedHomepageConfig = mutation({
  handler: async (ctx) => {
    // Check if data already exists
    const existingConfig = await ctx.db.query("homepageConfig").collect();
    if (existingConfig.length > 0) {
      return "Homepage config already exists";
    }

    const configs = [
      {
        sectionName: "social_cards",
        isEnabled: true,
        settings: {
          title: "Social Media Analytics",
          showMetrics: true,
          enableTweetEmbeds: true,
        },
        updatedBy: "system",
        updatedAt: Date.now(),
      },
      {
        sectionName: "case_studies",
        isEnabled: true,
        settings: {
          title: "CASE STUDIES",
          maxDisplay: 3,
          showMetrics: true,
        },
        updatedBy: "system",
        updatedAt: Date.now(),
      },
      {
        sectionName: "elon_counter",
        isEnabled: true,
        settings: {
          title: "Elon Counter",
          showRecentInteractions: true,
          maxInteractions: 3,
        },
        updatedBy: "system",
        updatedAt: Date.now(),
      },
    ];

    const insertedIds = [];
    for (const config of configs) {
      const id = await ctx.db.insert("homepageConfig", config);
      insertedIds.push(id);
    }

    return `Created ${insertedIds.length} homepage configs`;
  },
});

// Seed all data at once
export const seedAllData = mutation({
  handler: async (ctx) => {
    const results = [];
    
    try {
      const socialAccountsResult = await ctx.runMutation("seedData:seedSocialAccounts", {});
      results.push(socialAccountsResult);
      
      const caseStudiesResult = await ctx.runMutation("seedData:seedCaseStudies", {});
      results.push(caseStudiesResult);
      
      const elonInteractionsResult = await ctx.runMutation("seedData:seedElonInteractions", {});
      results.push(elonInteractionsResult);
      
      const homepageConfigResult = await ctx.runMutation("seedData:seedHomepageConfig", {});
      results.push(homepageConfigResult);
    } catch (error) {
      results.push(`Error: ${error}`);
    }
    
    return results;
  },
});

// Create initial admin user by email (easier setup)
export const createInitialAdminByEmail = mutation({
  args: {
    email: v.string(),
    name: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Check if any admin exists
    const existingAdmin = await ctx.db.query("adminUsers").first();
    if (existingAdmin) {
      throw new Error("Admin user already exists");
    }

    const adminId = await ctx.db.insert("adminUsers", {
      clerkUserId: "", // Will be populated when user first logs in
      email: args.email,
      name: args.name || "Super Admin",
      role: "super_admin",
      permissions: [
        "manage_social_cards",
        "manage_case_studies",
        "manage_homepage_config",
        "manage_admin_users",
        "view_analytics",
        "manage_elon_interactions",
      ],
      isActive: true,
      createdAt: Date.now(),
      lastActive: Date.now(),
    });

    return `Created initial admin user for ${args.email}. Admin access will be granted when they first log in.`;
  },
});

// Legacy function - keeping for backwards compatibility
export const createInitialAdmin = mutation({
  args: {
    clerkUserId: v.string(),
    email: v.string(),
    name: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Check if any admin exists
    const existingAdmin = await ctx.db.query("adminUsers").first();
    if (existingAdmin) {
      throw new Error("Admin user already exists");
    }

    const adminId = await ctx.db.insert("adminUsers", {
      clerkUserId: args.clerkUserId,
      email: args.email,
      name: args.name || "Super Admin",
      role: "super_admin",
      permissions: [
        "manage_social_cards",
        "manage_case_studies",
        "manage_homepage_config",
        "manage_admin_users",
        "view_analytics",
        "manage_elon_interactions",
      ],
      isActive: true,
      createdAt: Date.now(),
      lastActive: Date.now(),
    });

    return `Created initial admin user: ${adminId}`;
  },
});