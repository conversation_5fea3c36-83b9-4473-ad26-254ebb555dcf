/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as admin from "../admin.js";
import type * as caseStudies from "../caseStudies.js";
import type * as elonInteractions from "../elonInteractions.js";
import type * as healthCheck from "../healthCheck.js";
import type * as homepageConfig from "../homepageConfig.js";
import type * as mediaCaseStudies from "../mediaCaseStudies.js";
import type * as migrations from "../migrations.js";
import type * as seedData from "../seedData.js";
import type * as socialAccounts from "../socialAccounts.js";
import type * as todos from "../todos.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  admin: typeof admin;
  caseStudies: typeof caseStudies;
  elonInteractions: typeof elonInteractions;
  healthCheck: typeof healthCheck;
  homepageConfig: typeof homepageConfig;
  mediaCaseStudies: typeof mediaCaseStudies;
  migrations: typeof migrations;
  seedData: typeof seedData;
  socialAccounts: typeof socialAccounts;
  todos: typeof todos;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
