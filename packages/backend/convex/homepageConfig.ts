import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import { getAuthUserId } from "@convex-dev/auth/server";

// Admin authentication helper
async function requireAdmin(ctx: any) {
  const userId = await getAuthUserId(ctx);
  if (!userId) {
    throw new Error("Authentication required");
  }
  
  const adminUser = await ctx.db
    .query("adminUsers")
    .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", userId))
    .first();
    
  if (!adminUser || !adminUser.isActive) {
    throw new Error("Admin access required");
  }
  
  return adminUser;
}

// Get featured content configuration
export const getFeaturedConfig = query({
  handler: async (ctx) => {
    return await ctx.db
      .query("homepageConfig")
      .withIndex("by_section", (q) => q.eq("sectionName", "featured_content"))
      .first();
  },
});

// Update featured content configuration (admin only)
export const updateFeaturedConfig = mutation({
  args: {
    settings: v.any(), // JSON object for flexible configuration
  },
  handler: async (ctx, args) => {
    const admin = await requireAdmin(ctx);
    
    // Find existing config or create new one
    const existingConfig = await ctx.db
      .query("homepageConfig")
      .withIndex("by_section", (q) => q.eq("sectionName", "featured_content"))
      .first();
    
    if (existingConfig) {
      await ctx.db.patch(existingConfig._id, {
        settings: args.settings,
        updatedBy: admin.clerkUserId,
        updatedAt: Date.now(),
      });
    } else {
      await ctx.db.insert("homepageConfig", {
        sectionName: "featured_content",
        isEnabled: true,
        settings: args.settings,
        updatedBy: admin.clerkUserId,
        updatedAt: Date.now(),
      });
    }
    
    // Log the action
    await ctx.db.insert("adminAuditLog", {
      adminUserId: admin.clerkUserId,
      action: "update",
      resourceType: "homepage_config",
      changes: { section: "featured_content", settings: args.settings },
      timestamp: Date.now(),
    });
    
    return "success";
  },
});

// Get all homepage configurations (admin only)
export const getAllConfigs = query({
  handler: async (ctx) => {
    await requireAdmin(ctx);
    
    return await ctx.db
      .query("homepageConfig")
      .order("desc")
      .collect();
  },
});

// Update any homepage section configuration (admin only)
export const updateSectionConfig = mutation({
  args: {
    sectionName: v.string(),
    isEnabled: v.optional(v.boolean()),
    settings: v.optional(v.any()),
  },
  handler: async (ctx, args) => {
    const admin = await requireAdmin(ctx);
    
    // Find existing config or create new one
    const existingConfig = await ctx.db
      .query("homepageConfig")
      .withIndex("by_section", (q) => q.eq("sectionName", args.sectionName))
      .first();
    
    const updateData = {
      updatedBy: admin.clerkUserId,
      updatedAt: Date.now(),
      ...(args.isEnabled !== undefined && { isEnabled: args.isEnabled }),
      ...(args.settings !== undefined && { settings: args.settings }),
    };
    
    if (existingConfig) {
      await ctx.db.patch(existingConfig._id, updateData);
    } else {
      await ctx.db.insert("homepageConfig", {
        sectionName: args.sectionName,
        isEnabled: args.isEnabled ?? true,
        settings: args.settings || {},
        updatedBy: admin.clerkUserId,
        updatedAt: Date.now(),
      });
    }
    
    // Log the action
    await ctx.db.insert("adminAuditLog", {
      adminUserId: admin.clerkUserId,
      action: "update",
      resourceType: "homepage_config",
      changes: { section: args.sectionName, ...args },
      timestamp: Date.now(),
    });
    
    return "success";
  },
});