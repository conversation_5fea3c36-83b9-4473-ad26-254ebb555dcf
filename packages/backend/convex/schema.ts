import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  todos: defineTable({
    text: v.string(),
    completed: v.boolean(),
  }),
  
  // Admin users and permissions
  adminUsers: defineTable({
    clerkUserId: v.string(),
    email: v.string(),
    name: v.optional(v.string()),
    role: v.union(v.literal("super_admin"), v.literal("content_admin")),
    permissions: v.array(v.string()), // ["manage_social_cards", "manage_case_studies", etc.]
    isActive: v.boolean(),
    createdAt: v.number(),
    lastActive: v.number(),
  }).index("by_clerk_id", ["clerkUserId"])
    .index("by_email", ["email"]),
  
  // Dynamic social accounts for homepage
  socialAccounts: defineTable({
    handle: v.string(),           // @MarioNawfal, @RoundtableSpace, etc.
    displayName: v.string(),      // Mario <PERSON>l, Roundtable, etc.
    platform: v.string(),        // "X (Twitter)", "X Spaces"
    tweetUrl: v.optional(v.string()), // URL to embed real tweets
    monthlyImpressions: v.number(),
    monthlySpaceListeners: v.optional(v.number()),
    isActive: v.boolean(),
    displayOrder: v.number(),     // For homepage ordering
    createdAt: v.number(),
    updatedAt: v.number(),
    updatedBy: v.optional(v.string()), // Admin user ID
  }).index("by_active_order", ["isActive", "displayOrder"]),

  // Historical metrics (keeping for analytics)
  metrics: defineTable({
    accountId: v.id("socialAccounts"),
    month: v.string(),            // "2024-11"
    impressions: v.number(),
    spaceListeners: v.optional(v.number()),
    bestPostId: v.optional(v.string()),
    bestPostContent: v.optional(v.string()),
    bestPostMetrics: v.optional(v.object({
      likes: v.number(),
      reposts: v.number(),
      views: v.number(),
    })),
    updatedAt: v.number(),
  }),

  // Case studies with admin management
  caseStudies: defineTable({
    title: v.string(),
    category: v.union(v.literal("media"), v.literal("marketing")),
    description: v.string(),
    imageUrl: v.optional(v.string()),
    
    // Enhanced metadata for tweets showcase
    tweetUrl: v.optional(v.string()),           // Original tweet URL
    tweetId: v.optional(v.string()),            // Tweet ID for embedding
    accountHandle: v.optional(v.string()),      // @handle for filtering
    tags: v.array(v.string()),                  // ["crypto", "viral", "engagement"]
    
    metrics: v.object({
      impressions: v.optional(v.number()),
      engagement: v.optional(v.number()),
      conversions: v.optional(v.number()),
      likes: v.optional(v.number()),            // Tweet-specific metrics
      retweets: v.optional(v.number()),
      views: v.optional(v.number()),
    }),
    
    // Filtering and search enhancement
    searchableContent: v.string(),              // Combined title + description + tags for fuzzy search
    featured: v.boolean(),                      // Featured showcase priority
    performanceScore: v.optional(v.number()),   // Calculated score for sorting
    
    isActive: v.boolean(),
    displayOrder: v.number(),
    createdAt: v.number(),
    updatedAt: v.number(),
    updatedBy: v.optional(v.string()), // Admin user ID
  }).index("by_active_order", ["isActive", "displayOrder"])
    .index("by_category", ["category", "isActive"])
    .index("by_tags", ["tags"])
    .index("by_account", ["accountHandle"])
    .index("by_performance", ["performanceScore"])
    .searchIndex("search_content", {
      searchField: "searchableContent",
      filterFields: ["category", "featured", "isActive"]
    }),

  // Elon interactions
  elonInteractions: defineTable({
    type: v.union(v.literal("retweet"), v.literal("mention"), v.literal("reply")),
    tweetId: v.string(),
    content: v.string(),
    timestamp: v.number(),
    sourceAccount: v.string(),
    isActive: v.boolean(),
  }),

  // Tweet embeds cache for performance
  tweetEmbeds: defineTable({
    tweetUrl: v.string(),
    embedHtml: v.optional(v.string()),
    authorHandle: v.string(),
    content: v.string(),
    metrics: v.object({
      likes: v.number(),
      retweets: v.number(),
      views: v.optional(v.number()),
    }),
    cachedAt: v.number(),
    expiresAt: v.number(), // Cache for 24 hours
  }).index("by_url", ["tweetUrl"]),

  // Homepage configuration settings
  homepageConfig: defineTable({
    sectionName: v.string(), // "hero", "social_cards", "case_studies", "elon_counter"
    isEnabled: v.boolean(),
    settings: v.any(), // JSON object for flexible configuration
    updatedBy: v.string(), // Admin user ID
    updatedAt: v.number(),
  }).index("by_section", ["sectionName"]),

  // Audit log for admin actions
  adminAuditLog: defineTable({
    adminUserId: v.string(),
    action: v.string(), // "create", "update", "delete", "publish"
    resourceType: v.string(), // "social_account", "case_study", "homepage_config"
    resourceId: v.optional(v.string()),
    changes: v.any(), // JSON object describing what changed
    timestamp: v.number(),
  }).index("by_admin", ["adminUserId"]),
});
