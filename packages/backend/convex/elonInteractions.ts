import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import { getAuthUserId } from "@convex-dev/auth/server";

// Admin authentication helper
async function requireAdmin(ctx: any) {
  const userId = await getAuthUserId(ctx);
  if (!userId) {
    throw new Error("Authentication required");
  }
  
  const adminUser = await ctx.db
    .query("adminUsers")
    .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", userId))
    .first();
    
  if (!adminUser || !adminUser.isActive) {
    throw new Error("Admin access required");
  }
  
  return adminUser;
}

// Get Elon counter
export const getCount = query({
  handler: async (ctx) => {
    const config = await ctx.db
      .query("homepageConfig")
      .withIndex("by_section", (q) => q.eq("sectionName", "elon_counter"))
      .first();
      
    return config?.settings?.count || 7;
  },
});

// Get recent Elon interactions (public)
export const getRecent = query({
  args: { limit: v.optional(v.number()) },
  handler: async (ctx, args) => {
    return await ctx.db.query("elonInteractions")
      .filter((q) => q.eq(q.field("isActive"), true))
      .order("desc")
      .take(args.limit || 5);
  },
});

// Get all Elon interactions for admin
export const getAll = query({
  handler: async (ctx) => {
    await requireAdmin(ctx);
    
    return await ctx.db
      .query("elonInteractions")
      .order("desc")
      .collect();
  },
});

// Create Elon interaction (admin only)
export const createInteraction = mutation({
  args: {
    type: v.union(v.literal("retweet"), v.literal("mention"), v.literal("reply")),
    tweetId: v.string(),
    content: v.string(),
    sourceAccount: v.string(),
    timestamp: v.number(),
  },
  handler: async (ctx, args) => {
    const admin = await requireAdmin(ctx);
    
    const interactionId = await ctx.db.insert("elonInteractions", {
      ...args,
      isActive: true,
    });
    
    // Log the action
    await ctx.db.insert("adminAuditLog", {
      adminUserId: admin.clerkUserId,
      action: "create",
      resourceType: "elon_interaction",
      resourceId: interactionId,
      changes: { created: args },
      timestamp: Date.now(),
    });
    
    return interactionId;
  },
});

// Update Elon interaction (admin only)
export const updateInteraction = mutation({
  args: {
    id: v.id("elonInteractions"),
    type: v.optional(v.union(v.literal("retweet"), v.literal("mention"), v.literal("reply"))),
    tweetId: v.optional(v.string()),
    content: v.optional(v.string()),
    sourceAccount: v.optional(v.string()),
    isActive: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const admin = await requireAdmin(ctx);
    
    const { id, ...updates } = args;
    
    // Get current interaction for audit trail
    const currentInteraction = await ctx.db.get(id);
    if (!currentInteraction) {
      throw new Error("Elon interaction not found");
    }
    
    const filteredUpdates = Object.fromEntries(
      Object.entries(updates).filter(([_, value]) => value !== undefined)
    );
    
    await ctx.db.patch(id, filteredUpdates);
    
    // Log the action
    await ctx.db.insert("adminAuditLog", {
      adminUserId: admin.clerkUserId,
      action: "update",
      resourceType: "elon_interaction",
      resourceId: id,
      changes: { 
        before: currentInteraction,
        after: filteredUpdates 
      },
      timestamp: Date.now(),
    });
    
    return id;
  },
});

// Delete Elon interaction (admin only)
export const deleteInteraction = mutation({
  args: { id: v.id("elonInteractions") },
  handler: async (ctx, args) => {
    const admin = await requireAdmin(ctx);
    
    const interaction = await ctx.db.get(args.id);
    if (!interaction) {
      throw new Error("Elon interaction not found");
    }
    
    await ctx.db.delete(args.id);
    
    // Log the action
    await ctx.db.insert("adminAuditLog", {
      adminUserId: admin.clerkUserId,
      action: "delete",
      resourceType: "elon_interaction",
      resourceId: args.id,
      changes: { deleted: interaction },
      timestamp: Date.now(),
    });
    
    return args.id;
  },
});

// Update Elon counter (admin only)
export const updateCount = mutation({
  args: { count: v.number() },
  handler: async (ctx, args) => {
    const admin = await requireAdmin(ctx);
    
    // Find existing config or create new one
    const existingConfig = await ctx.db
      .query("homepageConfig")
      .withIndex("by_section", (q) => q.eq("sectionName", "elon_counter"))
      .first();
    
    if (existingConfig) {
      await ctx.db.patch(existingConfig._id, {
        settings: { ...existingConfig.settings, count: args.count },
        updatedBy: admin.clerkUserId,
        updatedAt: Date.now(),
      });
    } else {
      await ctx.db.insert("homepageConfig", {
        sectionName: "elon_counter",
        isEnabled: true,
        settings: { count: args.count },
        updatedBy: admin.clerkUserId,
        updatedAt: Date.now(),
      });
    }
    
    // Log the action
    await ctx.db.insert("adminAuditLog", {
      adminUserId: admin.clerkUserId,
      action: "update",
      resourceType: "elon_counter",
      changes: { count: args.count },
      timestamp: Date.now(),
    });
    
    return args.count;
  },
});

// Legacy function - keeping for backward compatibility
export const create = mutation({
  args: {
    type: v.union(v.literal("retweet"), v.literal("mention"), v.literal("reply")),
    tweetId: v.string(),
    content: v.string(),
    sourceAccount: v.string(),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("elonInteractions", {
      ...args,
      timestamp: Date.now(),
      isActive: true,
    });
  },
});