import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import { getAuthUserId } from "@convex-dev/auth/server";

// Admin authentication helper
async function requireAdmin(ctx: any) {
  const userId = await getAuthUserId(ctx);
  if (!userId) {
    throw new Error("Authentication required");
  }
  
  const adminUser = await ctx.db
    .query("adminUsers")
    .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", userId))
    .first();
    
  if (!adminUser || !adminUser.isActive) {
    throw new Error("Admin access required");
  }
  
  return adminUser;
}

// Check if current user is admin (for frontend conditional rendering)
export const isCurrentUserAdmin = query({
  handler: async (ctx) => {
    try {
      const userId = await getAuthUserId(ctx);
      if (!userId) {
        return false;
      }
      
      const adminUser = await ctx.db
        .query("adminUsers")
        .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", userId))
        .first();
        
      return adminUser && adminUser.isActive;
    } catch {
      return false;
    }
  },
});

// Get current admin user details
export const getCurrentAdminUser = query({
  handler: async (ctx) => {
    try {
      const userId = await getAuthUserId(ctx);
      if (!userId) {
        return null;
      }
      
      const adminUser = await ctx.db
        .query("adminUsers")
        .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", userId))
        .first();
        
      if (!adminUser || !adminUser.isActive) {
        return null;
      }
      
      return {
        id: adminUser._id,
        email: adminUser.email,
        name: adminUser.name,
        role: adminUser.role,
        permissions: adminUser.permissions,
      };
    } catch {
      return null;
    }
  },
});

// Get all active case studies ordered by display order
export const getAll = query({
  handler: async (ctx) => {
    return await ctx.db.query("caseStudies")
      .withIndex("by_active_order", (q) => q.eq("isActive", true))
      .order("asc")
      .collect();
  },
});

// Get all case studies for admin (includes inactive)
export const getAdminCaseStudies = query({
  handler: async (ctx) => {
    await requireAdmin(ctx);
    
    return await ctx.db
      .query("caseStudies")
      .order("desc")
      .collect();
  },
});

// Legacy function - keeping for backward compatibility
export const getFeatured = query({
  handler: async (ctx) => {
    return await ctx.db.query("caseStudies")
      .withIndex("by_active_order", (q) => q.eq("isActive", true))
      .order("asc")
      .take(10);
  },
});

export const getByCategory = query({
  args: { category: v.union(v.literal("media"), v.literal("marketing")) },
  handler: async (ctx, args) => {
    return await ctx.db.query("caseStudies")
      .filter((q) => q.eq(q.field("category"), args.category))
      .order("desc")
      .collect();
  },
});

export const getBestMedia = query({
  handler: async (ctx) => {
    return await ctx.db.query("caseStudies")
      .filter((q) => q.and(
        q.eq(q.field("category"), "media"),
        q.eq(q.field("featured"), true)
      ))
      .order("desc")
      .first();
  },
});

export const getBestMarketing = query({
  handler: async (ctx) => {
    return await ctx.db.query("caseStudies")
      .filter((q) => q.and(
        q.eq(q.field("category"), "marketing"),
        q.eq(q.field("featured"), true)
      ))
      .order("desc")
      .first();
  },
});

// Helper functions for new schema fields
function calculatePerformanceScore(metrics: any): number {
  const { views = 0, likes = 0, retweets = 0, engagement = 0, impressions = 0 } = metrics;
  
  // Weighted scoring: engagement rate, viral potential, reach
  const engagementRate = impressions > 0 ? (likes + retweets) / impressions : 0;
  const viralScore = retweets > 0 ? likes / retweets : likes;
  const reachScore = Math.log10(Math.max(1, views || impressions));
  
  return Math.round((engagementRate * 1000) + (viralScore * 10) + (reachScore * 100));
}

function generateSearchableContent(title: string, description: string, tags: string[] = [], accountHandle?: string): string {
  const handleText = accountHandle ? accountHandle.replace('@', '') : '';
  return [title, description, ...tags, handleText].filter(Boolean).join(' ').toLowerCase();
}

// Create case study (admin only)
export const createCaseStudy = mutation({
  args: {
    title: v.string(),
    category: v.union(v.literal("media"), v.literal("marketing")),
    description: v.string(),
    imageUrl: v.optional(v.string()),
    tweetUrl: v.optional(v.string()),
    tweetId: v.optional(v.string()),
    accountHandle: v.optional(v.string()),
    tags: v.optional(v.array(v.string())),
    metrics: v.object({
      impressions: v.optional(v.number()),
      engagement: v.optional(v.number()),
      conversions: v.optional(v.number()),
      likes: v.optional(v.number()),
      retweets: v.optional(v.number()),
      views: v.optional(v.number()),
    }),
    featured: v.optional(v.boolean()),
    displayOrder: v.number(),
  },
  handler: async (ctx, args) => {
    const admin = await requireAdmin(ctx);
    
    const tags = args.tags || [];
    const featured = args.featured ?? false;
    const performanceScore = calculatePerformanceScore(args.metrics);
    const searchableContent = generateSearchableContent(
      args.title, 
      args.description, 
      tags, 
      args.accountHandle
    );
    
    const caseStudyId = await ctx.db.insert("caseStudies", {
      title: args.title,
      category: args.category,
      description: args.description,
      imageUrl: args.imageUrl,
      tweetUrl: args.tweetUrl,
      tweetId: args.tweetId,
      accountHandle: args.accountHandle,
      tags,
      metrics: args.metrics,
      searchableContent,
      featured,
      performanceScore,
      isActive: true,
      displayOrder: args.displayOrder,
      createdAt: Date.now(),
      updatedAt: Date.now(),
      updatedBy: admin.clerkUserId,
    });
    
    // Log the action
    await ctx.db.insert("adminAuditLog", {
      adminUserId: admin.clerkUserId,
      action: "create",
      resourceType: "case_study",
      resourceId: caseStudyId,
      changes: { created: args },
      timestamp: Date.now(),
    });
    
    return caseStudyId;
  },
});

// Update case study (admin only)
export const updateCaseStudy = mutation({
  args: {
    id: v.id("caseStudies"),
    title: v.optional(v.string()),
    category: v.optional(v.union(v.literal("media"), v.literal("marketing"))),
    description: v.optional(v.string()),
    imageUrl: v.optional(v.string()),
    tweetUrl: v.optional(v.string()),
    tweetId: v.optional(v.string()),
    accountHandle: v.optional(v.string()),
    tags: v.optional(v.array(v.string())),
    metrics: v.optional(v.object({
      impressions: v.optional(v.number()),
      engagement: v.optional(v.number()),
      conversions: v.optional(v.number()),
      likes: v.optional(v.number()),
      retweets: v.optional(v.number()),
      views: v.optional(v.number()),
    })),
    featured: v.optional(v.boolean()),
    displayOrder: v.optional(v.number()),
    isActive: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const admin = await requireAdmin(ctx);
    
    const { id, ...updates } = args;
    
    // Get current case study for audit trail
    const currentCaseStudy = await ctx.db.get(id);
    if (!currentCaseStudy) {
      throw new Error("Case study not found");
    }
    
    const filteredUpdates = Object.fromEntries(
      Object.entries(updates).filter(([_, value]) => value !== undefined)
    );
    
    // Recalculate derived fields if needed
    const updatedMetrics = updates.metrics || currentCaseStudy.metrics;
    const updatedTitle = updates.title || currentCaseStudy.title;
    const updatedDescription = updates.description || currentCaseStudy.description;
    const updatedTags = updates.tags || currentCaseStudy.tags || [];
    const updatedAccountHandle = updates.accountHandle || currentCaseStudy.accountHandle;
    
    const performanceScore = calculatePerformanceScore(updatedMetrics);
    const searchableContent = generateSearchableContent(
      updatedTitle,
      updatedDescription,
      updatedTags,
      updatedAccountHandle
    );
    
    await ctx.db.patch(id, {
      ...filteredUpdates,
      performanceScore,
      searchableContent,
      updatedAt: Date.now(),
      updatedBy: admin.clerkUserId,
    });
    
    // Log the action
    await ctx.db.insert("adminAuditLog", {
      adminUserId: admin.clerkUserId,
      action: "update",
      resourceType: "case_study",
      resourceId: id,
      changes: { 
        before: currentCaseStudy,
        after: filteredUpdates 
      },
      timestamp: Date.now(),
    });
    
    return id;
  },
});

// Delete case study (admin only)
export const deleteCaseStudy = mutation({
  args: { id: v.id("caseStudies") },
  handler: async (ctx, args) => {
    const admin = await requireAdmin(ctx);
    
    const caseStudy = await ctx.db.get(args.id);
    if (!caseStudy) {
      throw new Error("Case study not found");
    }
    
    await ctx.db.delete(args.id);
    
    // Log the action
    await ctx.db.insert("adminAuditLog", {
      adminUserId: admin.clerkUserId,
      action: "delete",
      resourceType: "case_study",
      resourceId: args.id,
      changes: { deleted: caseStudy },
      timestamp: Date.now(),
    });
    
    return args.id;
  },
});

// Legacy function - keeping for backward compatibility
export const create = mutation({
  args: {
    title: v.string(),
    category: v.union(v.literal("media"), v.literal("marketing")),
    description: v.string(),
    imageUrl: v.optional(v.string()),
    metrics: v.object({
      impressions: v.optional(v.number()),
      engagement: v.optional(v.number()),
      conversions: v.optional(v.number()),
    }),
    featured: v.boolean(),
  },
  handler: async (ctx, args) => {
    const tags: string[] = [];
    const performanceScore = calculatePerformanceScore(args.metrics);
    const searchableContent = generateSearchableContent(args.title, args.description, tags);
    
    return await ctx.db.insert("caseStudies", {
      ...args,
      tags,
      searchableContent,
      featured: args.featured,
      performanceScore,
      isActive: true,
      displayOrder: 1,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });
  },
});