import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import { getAuthUserId } from "@convex-dev/auth/server";

// Admin authentication helper
async function requireAdmin(ctx: any) {
  const userId = await getAuthUserId(ctx);
  if (!userId) {
    throw new Error("Authentication required");
  }
  
  const adminUser = await ctx.db
    .query("adminUsers")
    .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", userId))
    .first();
    
  if (!adminUser || !adminUser.isActive) {
    throw new Error("Admin access required");
  }
  
  return adminUser;
}

// Helper function to calculate performance score
function calculatePerformanceScore(metrics: any): number {
  const { views = 0, likes = 0, retweets = 0, engagement = 0, impressions = 0 } = metrics;
  
  // Weighted scoring: engagement rate, viral potential, reach
  const engagementRate = impressions > 0 ? (likes + retweets) / impressions : 0;
  const viralScore = retweets > 0 ? likes / retweets : likes;
  const reachScore = Math.log10(Math.max(1, views || impressions));
  
  return Math.round((engagementRate * 1000) + (viralScore * 10) + (reachScore * 100));
}

// Helper function to generate searchable content
function generateSearchableContent(title: string, description: string, tags: string[], accountHandle?: string): string {
  const handleText = accountHandle ? accountHandle.replace('@', '') : '';
  return [title, description, ...tags, handleText].filter(Boolean).join(' ').toLowerCase();
}

// Get media case studies with filtering and sorting
export const getMediaCaseStudies = query({
  args: {
    tags: v.optional(v.array(v.string())),
    accountHandle: v.optional(v.string()),
    minViews: v.optional(v.number()),
    maxViews: v.optional(v.number()),
    minLikes: v.optional(v.number()),
    maxLikes: v.optional(v.number()),
    sortBy: v.optional(v.union(
      v.literal("performance"),
      v.literal("date"),
      v.literal("views"),
      v.literal("engagement")
    )),
    sortOrder: v.optional(v.union(v.literal("asc"), v.literal("desc"))),
    featuredOnly: v.optional(v.boolean()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    let query = ctx.db
      .query("caseStudies")
      .withIndex("by_category", (q) => q.eq("category", "media").eq("isActive", true));

    let results = await query.collect();

    // Apply filters
    if (args.tags && args.tags.length > 0) {
      results = results.filter(item => 
        args.tags!.some(tag => item.tags.includes(tag))
      );
    }

    if (args.accountHandle) {
      results = results.filter(item => item.accountHandle === args.accountHandle);
    }

    if (args.minViews !== undefined) {
      results = results.filter(item => (item.metrics.views || 0) >= args.minViews!);
    }

    if (args.maxViews !== undefined) {
      results = results.filter(item => (item.metrics.views || 0) <= args.maxViews!);
    }

    if (args.minLikes !== undefined) {
      results = results.filter(item => (item.metrics.likes || 0) >= args.minLikes!);
    }

    if (args.maxLikes !== undefined) {
      results = results.filter(item => (item.metrics.likes || 0) <= args.maxLikes!);
    }

    if (args.featuredOnly) {
      results = results.filter(item => item.featured);
    }

    // Apply sorting
    const sortBy = args.sortBy || "performance";
    const sortOrder = args.sortOrder || "desc";

    results.sort((a, b) => {
      let aValue: number, bValue: number;

      switch (sortBy) {
        case "performance":
          aValue = a.performanceScore || 0;
          bValue = b.performanceScore || 0;
          break;
        case "date":
          aValue = a.createdAt;
          bValue = b.createdAt;
          break;
        case "views":
          aValue = a.metrics.views || 0;
          bValue = b.metrics.views || 0;
          break;
        case "engagement":
          aValue = (a.metrics.likes || 0) + (a.metrics.retweets || 0);
          bValue = (b.metrics.likes || 0) + (b.metrics.retweets || 0);
          break;
        default:
          aValue = a.performanceScore || 0;
          bValue = b.performanceScore || 0;
      }

      return sortOrder === "desc" ? bValue - aValue : aValue - bValue;
    });

    // Apply limit
    if (args.limit) {
      results = results.slice(0, args.limit);
    }

    return results;
  },
});

// Search case studies using full-text search
export const searchCaseStudies = query({
  args: {
    searchTerm: v.string(),
    category: v.optional(v.union(v.literal("media"), v.literal("marketing"))),
    featuredOnly: v.optional(v.boolean()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const { searchTerm, category, featuredOnly, limit = 20 } = args;

    if (!searchTerm.trim()) {
      return [];
    }

    // Use Convex search index
    const searchResults = await ctx.db
      .query("caseStudies")
      .withSearchIndex("search_content", (q) => {
        let query = q.search("searchableContent", searchTerm);
        
        if (category) {
          query = query.eq("category", category);
        }
        
        if (featuredOnly) {
          query = query.eq("featured", true);
        }
        
        return query.eq("isActive", true);
      })
      .take(limit);

    return searchResults;
  },
});

// Get all unique tags
export const getCaseStudyTags = query({
  args: { category: v.optional(v.union(v.literal("media"), v.literal("marketing"))) },
  handler: async (ctx, args) => {
    let query = ctx.db.query("caseStudies");
    
    if (args.category) {
      query = query.withIndex("by_category", (q) => q.eq("category", args.category).eq("isActive", true));
    }

    const caseStudies = await query.collect();
    const allTags = caseStudies.flatMap(cs => cs.tags);
    const uniqueTags = [...new Set(allTags)].sort();
    
    return uniqueTags;
  },
});

// Get all unique account handles
export const getAccountHandles = query({
  args: { category: v.optional(v.union(v.literal("media"), v.literal("marketing"))) },
  handler: async (ctx, args) => {
    let query = ctx.db.query("caseStudies");
    
    if (args.category) {
      query = query.withIndex("by_category", (q) => q.eq("category", args.category).eq("isActive", true));
    }

    const caseStudies = await query.collect();
    const handles = caseStudies
      .map(cs => cs.accountHandle)
      .filter(Boolean)
      .filter((handle, index, self) => self.indexOf(handle) === index)
      .sort();
    
    return handles;
  },
});

// Update performance score for a case study
export const updatePerformanceScore = mutation({
  args: { id: v.id("caseStudies") },
  handler: async (ctx, args) => {
    const caseStudy = await ctx.db.get(args.id);
    if (!caseStudy) {
      throw new Error("Case study not found");
    }

    const performanceScore = calculatePerformanceScore(caseStudy.metrics);
    const searchableContent = generateSearchableContent(
      caseStudy.title, 
      caseStudy.description, 
      caseStudy.tags, 
      caseStudy.accountHandle
    );

    await ctx.db.patch(args.id, {
      performanceScore,
      searchableContent,
      updatedAt: Date.now(),
    });

    return { performanceScore, searchableContent };
  },
});

// Bulk update tags for multiple case studies
export const bulkUpdateTags = mutation({
  args: {
    caseStudyIds: v.array(v.id("caseStudies")),
    addTags: v.optional(v.array(v.string())),
    removeTags: v.optional(v.array(v.string())),
  },
  handler: async (ctx, args) => {
    const admin = await requireAdmin(ctx);
    const { caseStudyIds, addTags = [], removeTags = [] } = args;

    const results = [];

    for (const id of caseStudyIds) {
      const caseStudy = await ctx.db.get(id);
      if (!caseStudy) continue;

      let newTags = [...caseStudy.tags];

      // Add new tags
      for (const tag of addTags) {
        if (!newTags.includes(tag)) {
          newTags.push(tag);
        }
      }

      // Remove tags
      newTags = newTags.filter(tag => !removeTags.includes(tag));

      const searchableContent = generateSearchableContent(
        caseStudy.title, 
        caseStudy.description, 
        newTags, 
        caseStudy.accountHandle
      );

      await ctx.db.patch(id, {
        tags: newTags,
        searchableContent,
        updatedAt: Date.now(),
        updatedBy: admin.clerkUserId,
      });

      results.push({ id, tags: newTags });
    }

    // Log the action
    await ctx.db.insert("adminAuditLog", {
      adminUserId: admin.clerkUserId,
      action: "bulk_update",
      resourceType: "case_study_tags",
      changes: { 
        caseStudyIds,
        addTags,
        removeTags,
        results 
      },
      timestamp: Date.now(),
    });

    return results;
  },
});

// Get featured media case studies for homepage
export const getFeaturedMedia = query({
  args: { limit: v.optional(v.number()) },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("caseStudies")
      .withIndex("by_category", (q) => q.eq("category", "media").eq("isActive", true))
      .filter((q) => q.eq(q.field("featured"), true))
      .order("desc")
      .take(args.limit || 6);
  },
});

// Get metrics summary for media case studies
export const getMediaMetricsSummary = query({
  handler: async (ctx) => {
    const mediaCaseStudies = await ctx.db
      .query("caseStudies")
      .withIndex("by_category", (q) => q.eq("category", "media").eq("isActive", true))
      .collect();

    const totalCases = mediaCaseStudies.length;
    const totalViews = mediaCaseStudies.reduce((sum, cs) => sum + (cs.metrics.views || 0), 0);
    const totalLikes = mediaCaseStudies.reduce((sum, cs) => sum + (cs.metrics.likes || 0), 0);
    const totalRetweets = mediaCaseStudies.reduce((sum, cs) => sum + (cs.metrics.retweets || 0), 0);
    const totalImpressions = mediaCaseStudies.reduce((sum, cs) => sum + (cs.metrics.impressions || 0), 0);

    const avgEngagementRate = totalImpressions > 0 ? ((totalLikes + totalRetweets) / totalImpressions) * 100 : 0;

    return {
      totalCases,
      totalViews,
      totalLikes,
      totalRetweets,
      totalImpressions,
      avgEngagementRate: Math.round(avgEngagementRate * 100) / 100,
    };
  },
});