import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import { getAuthUserId } from "@convex-dev/auth/server";

// Admin authentication helper
async function requireAdmin(ctx: any) {
  const userId = await getAuthUserId(ctx);
  if (!userId) {
    throw new Error("Authentication required");
  }
  
  const adminUser = await ctx.db
    .query("adminUsers")
    .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", userId))
    .first();
    
  if (!adminUser || !adminUser.isActive) {
    throw new Error("Admin access required");
  }
  
  return adminUser;
}

// Get all active social accounts (public)
export const getAll = query({
  handler: async (ctx) => {
    return await ctx.db
      .query("socialAccounts")
      .withIndex("by_active_order", (q) => q.eq("isActive", true))
      .order("asc")
      .collect();
  },
});

// Get all social accounts for admin (includes inactive)
export const getAdminSocialAccounts = query({
  handler: async (ctx) => {
    await requireAdmin(ctx);
    
    return await ctx.db
      .query("socialAccounts")
      .order("desc")
      .collect();
  },
});

// Legacy function - keeping for backward compatibility
export const getAccountWithLatestMetrics = query({
  args: { handle: v.string() },
  handler: async (ctx, args) => {
    const account = await ctx.db.query("socialAccounts")
      .filter((q) => q.eq(q.field("handle"), args.handle))
      .first();
    
    if (!account) return null;

    const latestMetrics = await ctx.db.query("metrics")
      .filter((q) => q.eq(q.field("accountId"), account._id))
      .order("desc")
      .first();

    return {
      ...account,
      metrics: latestMetrics,
    };
  },
});

// Create social account (admin only)
export const createSocialAccount = mutation({
  args: {
    handle: v.string(),
    displayName: v.string(),
    platform: v.string(),
    tweetUrl: v.optional(v.string()),
    monthlyImpressions: v.number(),
    monthlySpaceListeners: v.optional(v.number()),
    displayOrder: v.number(),
  },
  handler: async (ctx, args) => {
    const admin = await requireAdmin(ctx);
    
    // Check if handle already exists
    const existing = await ctx.db
      .query("socialAccounts")
      .filter((q) => q.eq(q.field("handle"), args.handle))
      .first();
      
    if (existing) {
      throw new Error("Social account with this handle already exists");
    }
    
    const accountId = await ctx.db.insert("socialAccounts", {
      ...args,
      isActive: true,
      createdAt: Date.now(),
      updatedAt: Date.now(),
      updatedBy: admin.clerkUserId,
    });
    
    // Log the action
    await ctx.db.insert("adminAuditLog", {
      adminUserId: admin.clerkUserId,
      action: "create",
      resourceType: "social_account",
      resourceId: accountId,
      changes: { created: args },
      timestamp: Date.now(),
    });
    
    return accountId;
  },
});

// Update social account (admin only)
export const updateSocialAccount = mutation({
  args: {
    id: v.id("socialAccounts"),
    handle: v.optional(v.string()),
    displayName: v.optional(v.string()),
    platform: v.optional(v.string()),
    tweetUrl: v.optional(v.string()),
    monthlyImpressions: v.optional(v.number()),
    monthlySpaceListeners: v.optional(v.number()),
    displayOrder: v.optional(v.number()),
    isActive: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const admin = await requireAdmin(ctx);
    
    const { id, ...updates } = args;
    
    // Get current account for audit trail
    const currentAccount = await ctx.db.get(id);
    if (!currentAccount) {
      throw new Error("Social account not found");
    }
    
    // Check if handle change conflicts with existing
    if (updates.handle && updates.handle !== currentAccount.handle) {
      const existing = await ctx.db
        .query("socialAccounts")
        .filter((q) => q.eq(q.field("handle"), updates.handle))
        .first();
        
      if (existing && existing._id !== id) {
        throw new Error("Another social account with this handle already exists");
      }
    }
    
    const filteredUpdates = Object.fromEntries(
      Object.entries(updates).filter(([_, value]) => value !== undefined)
    );
    
    await ctx.db.patch(id, {
      ...filteredUpdates,
      updatedAt: Date.now(),
      updatedBy: admin.clerkUserId,
    });
    
    // Log the action
    await ctx.db.insert("adminAuditLog", {
      adminUserId: admin.clerkUserId,
      action: "update",
      resourceType: "social_account",
      resourceId: id,
      changes: { 
        before: currentAccount,
        after: filteredUpdates 
      },
      timestamp: Date.now(),
    });
    
    return id;
  },
});

// Delete social account (admin only)
export const deleteSocialAccount = mutation({
  args: { id: v.id("socialAccounts") },
  handler: async (ctx, args) => {
    const admin = await requireAdmin(ctx);
    
    const account = await ctx.db.get(args.id);
    if (!account) {
      throw new Error("Social account not found");
    }
    
    await ctx.db.delete(args.id);
    
    // Log the action
    await ctx.db.insert("adminAuditLog", {
      adminUserId: admin.clerkUserId,
      action: "delete",
      resourceType: "social_account",
      resourceId: args.id,
      changes: { deleted: account },
      timestamp: Date.now(),
    });
    
    return args.id;
  },
});

// Reorder social accounts (admin only)
export const reorderSocialAccounts = mutation({
  args: {
    accountOrders: v.array(v.object({
      id: v.id("socialAccounts"),
      displayOrder: v.number(),
    })),
  },
  handler: async (ctx, args) => {
    const admin = await requireAdmin(ctx);
    
    // Update all display orders
    for (const { id, displayOrder } of args.accountOrders) {
      await ctx.db.patch(id, {
        displayOrder,
        updatedAt: Date.now(),
        updatedBy: admin.clerkUserId,
      });
    }
    
    // Log the action
    await ctx.db.insert("adminAuditLog", {
      adminUserId: admin.clerkUserId,
      action: "reorder",
      resourceType: "social_account",
      changes: { newOrder: args.accountOrders },
      timestamp: Date.now(),
    });
    
    return "success";
  },
});

// Legacy functions - keeping for backward compatibility
export const create = mutation({
  args: {
    handle: v.string(),
    platform: v.string(),
    displayName: v.string(),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("socialAccounts", {
      ...args,
      isActive: true,
      createdAt: Date.now(),
      updatedAt: Date.now(),
      displayOrder: 0,
      monthlyImpressions: 0,
    });
  },
});

export const updateMetrics = mutation({
  args: {
    accountId: v.id("socialAccounts"),
    month: v.string(),
    impressions: v.number(),
    spaceListeners: v.optional(v.number()),
    bestPostContent: v.optional(v.string()),
    bestPostMetrics: v.optional(v.object({
      likes: v.number(),
      reposts: v.number(),
      views: v.number(),
    })),
  },
  handler: async (ctx, args) => {
    const existing = await ctx.db.query("metrics")
      .filter((q) => q.and(
        q.eq(q.field("accountId"), args.accountId),
        q.eq(q.field("month"), args.month)
      ))
      .first();

    if (existing) {
      return await ctx.db.patch(existing._id, {
        ...args,
        updatedAt: Date.now(),
      });
    } else {
      return await ctx.db.insert("metrics", {
        ...args,
        updatedAt: Date.now(),
      });
    }
  },
});