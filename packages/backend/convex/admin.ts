import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";

// Admin authentication helper
async function requireAdmin(ctx: any) {
  const userId = await getAuthUserId(ctx);
  if (!userId) {
    throw new Error("Authentication required");
  }
  
  const adminUser = await ctx.db
    .query("adminUsers")
    .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", userId))
    .first();
    
  if (!adminUser || !adminUser.isActive) {
    throw new Error("Admin access required");
  }
  
  return adminUser;
}

// Helper to find user by email
async function findUserByEmail(ctx: any, email: string) {
  // First check if it's already a Clerk user ID
  if (email.startsWith('user_')) {
    return email;
  }
  
  // Search for admin user by email
  const adminUser = await ctx.db
    .query("adminUsers")
    .withIndex("by_email", (q) => q.eq("email", email))
    .first();
    
  return adminUser?.clerkUserId || null;
}

// Check if user is admin
export const isAdmin = query({
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) return false;
    
    const adminUser = await ctx.db
      .query("adminUsers")
      .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", userId))
      .first();
      
    return adminUser?.isActive || false;
  },
});

// Get admin user info
export const getAdminUser = query({
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) return null;
    
    return await ctx.db
      .query("adminUsers")
      .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", userId))
      .first();
  },
});

// Create admin user by email (super admin only)
export const createAdminUserByEmail = mutation({
  args: {
    email: v.string(),
    name: v.optional(v.string()),
    role: v.union(v.literal("super_admin"), v.literal("content_admin")),
    permissions: v.array(v.string()),
  },
  handler: async (ctx, args) => {
    const admin = await requireAdmin(ctx);
    
    if (admin.role !== "super_admin") {
      throw new Error("Super admin access required");
    }
    
    const existingAdmin = await ctx.db
      .query("adminUsers")
      .withIndex("by_email", (q) => q.eq("email", args.email))
      .first();
      
    if (existingAdmin) {
      throw new Error("Admin user with this email already exists");
    }
    
    // For now, we'll create a placeholder entry that will be updated when the user first logs in
    // The clerkUserId will be updated when they authenticate
    const adminId = await ctx.db.insert("adminUsers", {
      clerkUserId: "", // Will be populated on first login
      email: args.email,
      name: args.name || "Admin User",
      role: args.role,
      permissions: args.permissions,
      isActive: true,
      createdAt: Date.now(),
      lastActive: Date.now(),
    });
    
    // Log the action
    await ctx.db.insert("adminAuditLog", {
      adminUserId: admin.clerkUserId,
      action: "create",
      resourceType: "admin_user",
      resourceId: adminId,
      changes: { created: args },
      timestamp: Date.now(),
    });
    
    return `Admin user created for ${args.email}. They will gain access when they first log in.`;
  },
});

// Update admin user's Clerk ID when they first log in
export const linkAdminAccount = mutation({
  args: {
    email: v.string(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) return { success: false, message: "Not authenticated" };
    
    // Find admin user by email without Clerk ID linked yet
    const adminUser = await ctx.db
      .query("adminUsers")
      .withIndex("by_email", (q) => q.eq("email", args.email))
      .first();
      
    if (adminUser && !adminUser.clerkUserId) {
      // Link the Clerk user ID
      await ctx.db.patch(adminUser._id, {
        clerkUserId: userId,
        lastActive: Date.now(),
      });
      
      // Log the linking action
      await ctx.db.insert("adminAuditLog", {
        adminUserId: userId,
        action: "link",
        resourceType: "admin_user",
        resourceId: adminUser._id,
        changes: { linked_clerk_id: userId },
        timestamp: Date.now(),
      });
      
      return { success: true, message: "Admin account linked successfully" };
    } else if (adminUser && adminUser.clerkUserId) {
      return { success: false, message: "Admin account already linked" };
    }
    
    return { success: false, message: "No admin user found with this email" };
  },
});

// Legacy function - keeping for backwards compatibility
export const createAdminUser = mutation({
  args: {
    clerkUserId: v.string(),
    email: v.string(),
    name: v.optional(v.string()),
    role: v.union(v.literal("super_admin"), v.literal("content_admin")),
    permissions: v.array(v.string()),
  },
  handler: async (ctx, args) => {
    const admin = await requireAdmin(ctx);
    
    if (admin.role !== "super_admin") {
      throw new Error("Super admin access required");
    }
    
    const existingAdmin = await ctx.db
      .query("adminUsers")
      .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", args.clerkUserId))
      .first();
      
    if (existingAdmin) {
      throw new Error("Admin user already exists");
    }
    
    const adminId = await ctx.db.insert("adminUsers", {
      ...args,
      isActive: true,
      createdAt: Date.now(),
      lastActive: Date.now(),
    });
    
    // Log the action
    await ctx.db.insert("adminAuditLog", {
      adminUserId: admin.clerkUserId,
      action: "create",
      resourceType: "admin_user",
      resourceId: adminId,
      changes: { created: args },
      timestamp: Date.now(),
    });
    
    return adminId;
  },
});

// List all admin users (super admin only)
export const getAllAdminUsers = query({
  handler: async (ctx) => {
    const admin = await requireAdmin(ctx);
    
    if (admin.role !== "super_admin") {
      throw new Error("Super admin access required");
    }
    
    const adminUsers = await ctx.db
      .query("adminUsers")
      .collect();
    
    // Sort by creation time, newest first
    return adminUsers.sort((a, b) => b.createdAt - a.createdAt);
  },
});

// Update admin user activity
export const updateAdminActivity = mutation({
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) return;
    
    const adminUser = await ctx.db
      .query("adminUsers")
      .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", userId))
      .first();
      
    if (adminUser) {
      await ctx.db.patch(adminUser._id, {
        lastActive: Date.now(),
      });
    }
  },
});