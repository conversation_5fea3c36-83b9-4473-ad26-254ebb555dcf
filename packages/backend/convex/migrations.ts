import { mutation } from "./_generated/server";

// Migration to add new fields to existing case studies
export const migrateExistingCaseStudies = mutation({
  handler: async (ctx) => {
    const caseStudies = await ctx.db.query("caseStudies").collect();
    
    for (const caseStudy of caseStudies) {
      // Check if case study needs migration
      const needsMigration = 
        caseStudy.tags === undefined ||
        caseStudy.searchableContent === undefined ||
        caseStudy.featured === undefined;
      
      if (needsMigration) {
        // Generate searchable content
        const tags = caseStudy.tags || [];
        const accountHandle = caseStudy.accountHandle || '';
        const handleText = accountHandle ? accountHandle.replace('@', '') : '';
        const searchableContent = [
          caseStudy.title, 
          caseStudy.description, 
          ...tags, 
          handleText
        ].filter(Boolean).join(' ').toLowerCase();
        
        // Calculate performance score
        const { views = 0, likes = 0, retweets = 0, engagement = 0, impressions = 0 } = caseStudy.metrics;
        const engagementRate = impressions > 0 ? (likes + retweets) / impressions : 0;
        const viralScore = retweets > 0 ? likes / retweets : likes;
        const reachScore = Math.log10(Math.max(1, views || impressions));
        const performanceScore = Math.round((engagementRate * 1000) + (viralScore * 10) + (reachScore * 100));
        
        await ctx.db.patch(caseStudy._id, {
          tags: tags,
          searchableContent,
          featured: caseStudy.featured ?? false,
          performanceScore,
        });
      }
    }
    
    return { migratedCount: caseStudies.filter(cs => 
      cs.tags === undefined ||
      cs.searchableContent === undefined ||
      cs.featured === undefined
    ).length };
  },
});