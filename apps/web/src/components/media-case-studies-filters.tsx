import React, { useState } from "react";
import { 
  Filter, 
  X, 
  Hash, 
  User, 
  TrendingUp, 
  Calendar,
  Grid,
  List,
  Eye,
  Heart,
  Settings
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { Slider } from "@/components/ui/slider";

export interface FilterState {
  tags: string[];
  accountHandle?: string;
  minViews?: number;
  maxViews?: number;
  minLikes?: number;
  maxLikes?: number;
  sortBy: "performance" | "date" | "views" | "engagement";
  sortOrder: "asc" | "desc";
  featuredOnly: boolean;
  viewMode: "grid" | "list";
  showMetrics: boolean;
}

interface MediaCaseStudiesFiltersProps {
  filters: FilterState;
  onFiltersChange: (filters: FilterState) => void;
  availableTags: string[];
  availableAccounts: string[];
  totalResults: number;
}

const DEFAULT_FILTERS: FilterState = {
  tags: [],
  sortBy: "performance",
  sortOrder: "desc",
  featuredOnly: false,
  viewMode: "grid",
  showMetrics: true,
};

export function MediaCaseStudiesFilters({
  filters,
  onFiltersChange,
  availableTags,
  availableAccounts,
  totalResults,
}: MediaCaseStudiesFiltersProps) {
  const [isFiltersOpen, setIsFiltersOpen] = useState(false);
  const [tagSearch, setTagSearch] = useState("");
  const [accountSearch, setAccountSearch] = useState("");

  const hasActiveFilters = 
    filters.tags.length > 0 ||
    filters.accountHandle ||
    filters.minViews ||
    filters.maxViews ||
    filters.minLikes ||
    filters.maxLikes ||
    filters.featuredOnly;

  const clearAllFilters = () => {
    onFiltersChange(DEFAULT_FILTERS);
  };

  const toggleTag = (tag: string) => {
    const newTags = filters.tags.includes(tag)
      ? filters.tags.filter(t => t !== tag)
      : [...filters.tags, tag];
    onFiltersChange({ ...filters, tags: newTags });
  };

  const removeTag = (tag: string) => {
    onFiltersChange({ 
      ...filters, 
      tags: filters.tags.filter(t => t !== tag) 
    });
  };

  const filteredTags = availableTags.filter(tag =>
    tag.toLowerCase().includes(tagSearch.toLowerCase())
  );

  const filteredAccounts = availableAccounts.filter(account =>
    account.toLowerCase().includes(accountSearch.toLowerCase())
  );

  const updateFilter = (key: keyof FilterState, value: any) => {
    onFiltersChange({ ...filters, [key]: value });
  };

  return (
    <div className="space-y-4">
      {/* Top Bar with View Controls and Filter Button */}
      <div className="flex items-center justify-between gap-4">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">
              {totalResults} case studies
            </span>
          </div>

          {/* Active Filters */}
          {hasActiveFilters && (
            <div className="flex items-center gap-2">
              {filters.tags.map(tag => (
                <Badge key={tag} variant="secondary" className="text-xs">
                  #{tag}
                  <X 
                    className="h-3 w-3 ml-1 cursor-pointer" 
                    onClick={() => removeTag(tag)}
                  />
                </Badge>
              ))}
              
              {filters.accountHandle && (
                <Badge variant="secondary" className="text-xs">
                  {filters.accountHandle}
                  <X 
                    className="h-3 w-3 ml-1 cursor-pointer" 
                    onClick={() => updateFilter('accountHandle', undefined)}
                  />
                </Badge>
              )}
              
              {filters.featuredOnly && (
                <Badge variant="secondary" className="text-xs">
                  Featured Only
                  <X 
                    className="h-3 w-3 ml-1 cursor-pointer" 
                    onClick={() => updateFilter('featuredOnly', false)}
                  />
                </Badge>
              )}

              <Button
                variant="ghost"
                size="sm"
                onClick={clearAllFilters}
                className="text-xs h-6"
              >
                Clear all
              </Button>
            </div>
          )}
        </div>

        <div className="flex items-center gap-2">
          {/* Sort Select */}
          <Select 
            value={`${filters.sortBy}-${filters.sortOrder}`} 
            onValueChange={(value) => {
              const [sortBy, sortOrder] = value.split('-') as [typeof filters.sortBy, typeof filters.sortOrder];
              onFiltersChange({ ...filters, sortBy, sortOrder });
            }}
          >
            <SelectTrigger className="w-44">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="performance-desc">Performance (High to Low)</SelectItem>
              <SelectItem value="performance-asc">Performance (Low to High)</SelectItem>
              <SelectItem value="date-desc">Newest First</SelectItem>
              <SelectItem value="date-asc">Oldest First</SelectItem>
              <SelectItem value="views-desc">Most Views</SelectItem>
              <SelectItem value="views-asc">Least Views</SelectItem>
              <SelectItem value="engagement-desc">Most Engagement</SelectItem>
              <SelectItem value="engagement-asc">Least Engagement</SelectItem>
            </SelectContent>
          </Select>

          {/* View Mode Toggle */}
          <div className="flex border rounded-md">
            <Button
              variant={filters.viewMode === "grid" ? "default" : "ghost"}
              size="sm"
              onClick={() => updateFilter('viewMode', 'grid')}
              className="rounded-r-none px-3"
            >
              <Grid className="h-4 w-4" />
            </Button>
            <Button
              variant={filters.viewMode === "list" ? "default" : "ghost"}
              size="sm"
              onClick={() => updateFilter('viewMode', 'list')}
              className="rounded-l-none px-3"
            >
              <List className="h-4 w-4" />
            </Button>
          </div>

          {/* Filters Sheet */}
          <Sheet open={isFiltersOpen} onOpenChange={setIsFiltersOpen}>
            <SheetTrigger asChild>
              <Button variant="outline" size="sm">
                <Filter className="h-4 w-4 mr-2" />
                Filters
                {hasActiveFilters && (
                  <Badge variant="secondary" className="ml-2 h-5 text-xs">
                    {filters.tags.length + (filters.accountHandle ? 1 : 0) + (filters.featuredOnly ? 1 : 0)}
                  </Badge>
                )}
              </Button>
            </SheetTrigger>
            <SheetContent className="w-80">
              <SheetHeader>
                <SheetTitle>Filter Case Studies</SheetTitle>
                <SheetDescription>
                  Refine your search with advanced filters
                </SheetDescription>
              </SheetHeader>

              <div className="space-y-6 mt-6">
                {/* Featured Only */}
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="featured"
                      checked={filters.featuredOnly}
                      onCheckedChange={(checked) => updateFilter('featuredOnly', checked)}
                    />
                    <Label htmlFor="featured" className="flex items-center gap-2">
                      <TrendingUp className="h-4 w-4" />
                      Featured only
                    </Label>
                  </div>
                </div>

                {/* Display Options */}
                <Collapsible defaultOpen>
                  <CollapsibleTrigger className="flex items-center gap-2 font-medium">
                    <Settings className="h-4 w-4" />
                    Display Options
                  </CollapsibleTrigger>
                  <CollapsibleContent className="space-y-3 mt-3">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="showMetrics"
                        checked={filters.showMetrics}
                        onCheckedChange={(checked) => updateFilter('showMetrics', checked)}
                      />
                      <Label htmlFor="showMetrics">Show metrics</Label>
                    </div>
                  </CollapsibleContent>
                </Collapsible>

                {/* Tags Filter */}
                <Collapsible defaultOpen>
                  <CollapsibleTrigger className="flex items-center gap-2 font-medium">
                    <Hash className="h-4 w-4" />
                    Tags ({filters.tags.length} selected)
                  </CollapsibleTrigger>
                  <CollapsibleContent className="space-y-3 mt-3">
                    <Input
                      placeholder="Search tags..."
                      value={tagSearch}
                      onChange={(e) => setTagSearch(e.target.value)}
                    />
                    <div className="max-h-32 overflow-y-auto space-y-2">
                      {filteredTags.map(tag => (
                        <div key={tag} className="flex items-center space-x-2">
                          <Checkbox
                            id={`tag-${tag}`}
                            checked={filters.tags.includes(tag)}
                            onCheckedChange={() => toggleTag(tag)}
                          />
                          <Label htmlFor={`tag-${tag}`} className="text-sm">
                            #{tag}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </CollapsibleContent>
                </Collapsible>

                {/* Account Filter */}
                <Collapsible defaultOpen>
                  <CollapsibleTrigger className="flex items-center gap-2 font-medium">
                    <User className="h-4 w-4" />
                    Account
                  </CollapsibleTrigger>
                  <CollapsibleContent className="space-y-3 mt-3">
                    <Select
                      value={filters.accountHandle || ""}
                      onValueChange={(value) => updateFilter('accountHandle', value || undefined)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select account" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">All accounts</SelectItem>
                        {availableAccounts.map(account => (
                          <SelectItem key={account} value={account}>
                            {account}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </CollapsibleContent>
                </Collapsible>

                {/* Views Range */}
                <Collapsible>
                  <CollapsibleTrigger className="flex items-center gap-2 font-medium">
                    <Eye className="h-4 w-4" />
                    Views Range
                  </CollapsibleTrigger>
                  <CollapsibleContent className="space-y-3 mt-3">
                    <div className="grid grid-cols-2 gap-2">
                      <div>
                        <Label className="text-xs">Min Views</Label>
                        <Input
                          type="number"
                          placeholder="0"
                          value={filters.minViews || ""}
                          onChange={(e) => updateFilter('minViews', e.target.value ? parseInt(e.target.value) : undefined)}
                        />
                      </div>
                      <div>
                        <Label className="text-xs">Max Views</Label>
                        <Input
                          type="number"
                          placeholder="No limit"
                          value={filters.maxViews || ""}
                          onChange={(e) => updateFilter('maxViews', e.target.value ? parseInt(e.target.value) : undefined)}
                        />
                      </div>
                    </div>
                  </CollapsibleContent>
                </Collapsible>

                {/* Likes Range */}
                <Collapsible>
                  <CollapsibleTrigger className="flex items-center gap-2 font-medium">
                    <Heart className="h-4 w-4" />
                    Likes Range
                  </CollapsibleTrigger>
                  <CollapsibleContent className="space-y-3 mt-3">
                    <div className="grid grid-cols-2 gap-2">
                      <div>
                        <Label className="text-xs">Min Likes</Label>
                        <Input
                          type="number"
                          placeholder="0"
                          value={filters.minLikes || ""}
                          onChange={(e) => updateFilter('minLikes', e.target.value ? parseInt(e.target.value) : undefined)}
                        />
                      </div>
                      <div>
                        <Label className="text-xs">Max Likes</Label>
                        <Input
                          type="number"
                          placeholder="No limit"
                          value={filters.maxLikes || ""}
                          onChange={(e) => updateFilter('maxLikes', e.target.value ? parseInt(e.target.value) : undefined)}
                        />
                      </div>
                    </div>
                  </CollapsibleContent>
                </Collapsible>
              </div>

              {/* Footer */}
              <div className="mt-6 pt-4 border-t space-y-2">
                <Button 
                  variant="outline" 
                  className="w-full" 
                  onClick={clearAllFilters}
                  disabled={!hasActiveFilters}
                >
                  Clear All Filters
                </Button>
                <Button 
                  className="w-full" 
                  onClick={() => setIsFiltersOpen(false)}
                >
                  Apply Filters
                </Button>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </div>
  );
}