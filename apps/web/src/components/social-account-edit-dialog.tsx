import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useMutation } from 'convex/react';
import { api } from '@backend/api';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { toast } from 'sonner';
import { Save, X } from 'lucide-react';

interface SocialAccount {
  _id: string;
  handle: string;
  displayName: string;
  platform: string;
  tweetUrl?: string;
  monthlyImpressions: number;
  monthlySpaceListeners?: number;
  isActive: boolean;
  displayOrder: number;
}

interface SocialAccountEditDialogProps {
  isOpen: boolean;
  onClose: () => void;
  socialAccount: SocialAccount | null;
  isCreating?: boolean;
}

export function SocialAccountEditDialog({
  isOpen,
  onClose,
  socialAccount,
  isCreating = false,
}: SocialAccountEditDialogProps) {
  const updateSocialAccount = useMutation(api.socialAccounts.updateSocialAccount);
  const createSocialAccount = useMutation(api.socialAccounts.createSocialAccount);

  const [formData, setFormData] = useState({
    handle: '',
    displayName: '',
    platform: '',
    tweetUrl: '',
    monthlyImpressions: 0,
    monthlySpaceListeners: 0,
    isActive: true,
    displayOrder: 1,
  });

  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (socialAccount && !isCreating) {
      setFormData({
        handle: socialAccount.handle,
        displayName: socialAccount.displayName,
        platform: socialAccount.platform,
        tweetUrl: socialAccount.tweetUrl || '',
        monthlyImpressions: socialAccount.monthlyImpressions,
        monthlySpaceListeners: socialAccount.monthlySpaceListeners || 0,
        isActive: socialAccount.isActive,
        displayOrder: socialAccount.displayOrder,
      });
    } else {
      setFormData({
        handle: '',
        displayName: '',
        platform: 'X (Twitter)',
        tweetUrl: '',
        monthlyImpressions: 0,
        monthlySpaceListeners: 0,
        isActive: true,
        displayOrder: 1,
      });
    }
  }, [socialAccount, isCreating, isOpen]);

  const handleSave = async () => {
    try {
      setIsLoading(true);

      if (isCreating) {
        await createSocialAccount({
          ...formData,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
        toast.success('Social account created successfully!');
      } else if (socialAccount) {
        await updateSocialAccount({
          id: socialAccount._id,
          ...formData,
          updatedAt: Date.now(),
        });
        toast.success('Social account updated successfully!');
      }

      onClose();
    } catch (error) {
      toast.error(`Failed to ${isCreating ? 'create' : 'update'} social account: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {isCreating ? 'Create New Social Account' : 'Edit Social Account'}
          </DialogTitle>
        </DialogHeader>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-6 py-4"
        >
          {/* Basic Info */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Basic Information</h3>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="handle">Handle</Label>
                <Input
                  id="handle"
                  value={formData.handle}
                  onChange={(e) => handleInputChange('handle', e.target.value)}
                  placeholder="@username"
                />
              </div>
              
              <div>
                <Label htmlFor="displayName">Display Name</Label>
                <Input
                  id="displayName"
                  value={formData.displayName}
                  onChange={(e) => handleInputChange('displayName', e.target.value)}
                  placeholder="Full Name"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="platform">Platform</Label>
              <Input
                id="platform"
                value={formData.platform}
                onChange={(e) => handleInputChange('platform', e.target.value)}
                placeholder="X (Twitter)"
              />
            </div>

            <div>
              <Label htmlFor="tweetUrl">Tweet URL (Optional)</Label>
              <Input
                id="tweetUrl"
                value={formData.tweetUrl}
                onChange={(e) => handleInputChange('tweetUrl', e.target.value)}
                placeholder="https://x.com/username/status/..."
              />
            </div>
          </div>

          {/* Metrics */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Metrics</h3>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="monthlyImpressions">Monthly Impressions</Label>
                <Input
                  id="monthlyImpressions"
                  type="number"
                  value={formData.monthlyImpressions}
                  onChange={(e) => handleInputChange('monthlyImpressions', parseInt(e.target.value) || 0)}
                  placeholder="0"
                />
              </div>
              
              <div>
                <Label htmlFor="monthlySpaceListeners">Monthly Space Listeners (Optional)</Label>
                <Input
                  id="monthlySpaceListeners"
                  type="number"
                  value={formData.monthlySpaceListeners}
                  onChange={(e) => handleInputChange('monthlySpaceListeners', parseInt(e.target.value) || 0)}
                  placeholder="0"
                />
              </div>
            </div>
          </div>

          {/* Settings */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Settings</h3>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="displayOrder">Display Order</Label>
                <Input
                  id="displayOrder"
                  type="number"
                  value={formData.displayOrder}
                  onChange={(e) => handleInputChange('displayOrder', parseInt(e.target.value) || 1)}
                  placeholder="1"
                />
              </div>
              
              <div className="flex items-center space-x-2">
                <Switch
                  id="isActive"
                  checked={formData.isActive}
                  onCheckedChange={(checked) => handleInputChange('isActive', checked)}
                />
                <Label htmlFor="isActive">Active</Label>
              </div>
            </div>
          </div>
        </motion.div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isLoading}
          >
            <X className="h-4 w-4 mr-2" />
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            disabled={isLoading}
          >
            <Save className="h-4 w-4 mr-2" />
            {isLoading ? 'Saving...' : (isCreating ? 'Create' : 'Save')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}