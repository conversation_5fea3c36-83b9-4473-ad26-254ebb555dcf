import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>eader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { 
  Eye, 
  Heart, 
  Repeat, 
  Share, 
  TrendingUp, 
  User, 
  ExternalLink,
  Copy
} from "lucide-react";
import { TweetEmbed } from "@/components/tweet-embed";
import { cn } from "@/lib/utils";

interface MediaCaseStudyCardProps {
  caseStudy: {
    _id: string;
    title: string;
    description: string;
    tweetUrl?: string;
    tweetId?: string;
    accountHandle?: string;
    tags: string[];
    metrics: {
      views?: number;
      likes?: number;
      retweets?: number;
      impressions?: number;
      engagement?: number;
    };
    featured: boolean;
    performanceScore?: number;
    createdAt: number;
  };
  viewMode?: 'grid' | 'list';
  showMetrics?: boolean;
  isHighlighted?: boolean;
  onShare?: (caseStudy: any) => void;
  onCopyUrl?: (url: string) => void;
}

export function MediaCaseStudyCard({ 
  caseStudy, 
  viewMode = 'grid',
  showMetrics = true,
  isHighlighted = false,
  onShare,
  onCopyUrl
}: MediaCaseStudyCardProps) {
  
  const formatMetric = (value?: number) => {
    if (!value) return "0";
    if (value >= 1000000) return `${(value / 1000000).toFixed(1)}M`;
    if (value >= 1000) return `${(value / 1000).toFixed(1)}K`;
    return value.toString();
  };

  const getPerformanceLevel = (score?: number) => {
    if (!score) return { level: "New", color: "bg-gray-500" };
    if (score >= 10000) return { level: "Viral", color: "bg-red-500" };
    if (score >= 5000) return { level: "High", color: "bg-orange-500" };
    if (score >= 1000) return { level: "Good", color: "bg-yellow-500" };
    return { level: "Standard", color: "bg-green-500" };
  };

  const performance = getPerformanceLevel(caseStudy.performanceScore);

  const handleCopyTweetUrl = () => {
    if (caseStudy.tweetUrl && onCopyUrl) {
      onCopyUrl(caseStudy.tweetUrl);
    }
  };

  const handleShare = () => {
    if (onShare) {
      onShare(caseStudy);
    }
  };

  if (viewMode === 'list') {
    return (
      <Card className={cn(
        "transition-all duration-200 hover:shadow-lg",
        isHighlighted && "ring-2 ring-primary ring-offset-2"
      )}>
        <div className="flex flex-col md:flex-row">
          {/* Tweet Embed Section */}
          <div className="md:w-96 flex-shrink-0">
            {caseStudy.tweetUrl && caseStudy.tweetId ? (
              <div className="h-full">
                <TweetEmbed 
                  tweetUrl={caseStudy.tweetUrl} 
                  className="h-full min-h-[300px]"
                />
              </div>
            ) : (
              <div className="h-full min-h-[300px] bg-muted flex items-center justify-center">
                <p className="text-muted-foreground">No tweet available</p>
              </div>
            )}
          </div>

          {/* Content Section */}
          <div className="flex-1 p-6">
            <CardHeader className="p-0 mb-4">
              <div className="flex items-start justify-between gap-4">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    {caseStudy.featured && (
                      <Badge variant="secondary" className="text-xs">
                        <TrendingUp className="h-3 w-3 mr-1" />
                        Featured
                      </Badge>
                    )}
                    <div className={cn("w-2 h-2 rounded-full", performance.color)} />
                    <span className="text-xs text-muted-foreground">{performance.level}</span>
                  </div>
                  
                  <h3 className="text-xl font-semibold leading-tight">{caseStudy.title}</h3>
                  
                  {caseStudy.accountHandle && (
                    <div className="flex items-center gap-1 mt-2 text-sm text-blue-600">
                      <User className="h-4 w-4" />
                      {caseStudy.accountHandle}
                    </div>
                  )}
                </div>

                <div className="flex items-center gap-2">
                  {caseStudy.tweetUrl && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleCopyTweetUrl}
                      className="text-xs"
                    >
                      <Copy className="h-3 w-3 mr-1" />
                      Copy URL
                    </Button>
                  )}
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleShare}
                    className="text-xs"
                  >
                    <Share className="h-3 w-3 mr-1" />
                    Share
                  </Button>
                </div>
              </div>
            </CardHeader>

            <CardContent className="p-0 mb-4">
              <p className="text-muted-foreground mb-4">{caseStudy.description}</p>

              {/* Tags */}
              {caseStudy.tags.length > 0 && (
                <div className="flex flex-wrap gap-2 mb-4">
                  {caseStudy.tags.map(tag => (
                    <Badge key={tag} variant="outline" className="text-xs">
                      #{tag}
                    </Badge>
                  ))}
                </div>
              )}

              {/* Metrics */}
              {showMetrics && (
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {caseStudy.metrics.views && (
                    <div className="text-center">
                      <div className="flex items-center justify-center gap-1 text-sm text-muted-foreground mb-1">
                        <Eye className="h-4 w-4" />
                        Views
                      </div>
                      <div className="text-lg font-semibold">
                        {formatMetric(caseStudy.metrics.views)}
                      </div>
                    </div>
                  )}

                  {caseStudy.metrics.likes && (
                    <div className="text-center">
                      <div className="flex items-center justify-center gap-1 text-sm text-muted-foreground mb-1">
                        <Heart className="h-4 w-4" />
                        Likes
                      </div>
                      <div className="text-lg font-semibold">
                        {formatMetric(caseStudy.metrics.likes)}
                      </div>
                    </div>
                  )}

                  {caseStudy.metrics.retweets && (
                    <div className="text-center">
                      <div className="flex items-center justify-center gap-1 text-sm text-muted-foreground mb-1">
                        <Repeat className="h-4 w-4" />
                        Retweets
                      </div>
                      <div className="text-lg font-semibold">
                        {formatMetric(caseStudy.metrics.retweets)}
                      </div>
                    </div>
                  )}

                  {caseStudy.metrics.impressions && (
                    <div className="text-center">
                      <div className="flex items-center justify-center gap-1 text-sm text-muted-foreground mb-1">
                        <TrendingUp className="h-4 w-4" />
                        Impressions
                      </div>
                      <div className="text-lg font-semibold">
                        {formatMetric(caseStudy.metrics.impressions)}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </div>
        </div>
      </Card>
    );
  }

  // Grid view (default)
  return (
    <Card className={cn(
      "transition-all duration-200 hover:shadow-lg group",
      isHighlighted && "ring-2 ring-primary ring-offset-2"
    )}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between gap-2">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              {caseStudy.featured && (
                <Badge variant="secondary" className="text-xs">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  Featured
                </Badge>
              )}
              <div className={cn("w-2 h-2 rounded-full", performance.color)} />
              <span className="text-xs text-muted-foreground">{performance.level}</span>
            </div>
            
            <h3 className="font-semibold leading-tight line-clamp-2">{caseStudy.title}</h3>
            
            {caseStudy.accountHandle && (
              <div className="flex items-center gap-1 mt-2 text-sm text-blue-600">
                <User className="h-4 w-4" />
                {caseStudy.accountHandle}
              </div>
            )}
          </div>

          <div className="opacity-0 group-hover:opacity-100 transition-opacity">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleShare}
              className="h-8 w-8 p-0"
            >
              <Share className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="pb-3">
        {/* Tweet Embed */}
        {caseStudy.tweetUrl && caseStudy.tweetId ? (
          <div className="mb-4">
            <TweetEmbed 
              tweetUrl={caseStudy.tweetUrl} 
              className="max-h-96 overflow-hidden"
            />
          </div>
        ) : (
          <div className="h-32 bg-muted rounded-md flex items-center justify-center mb-4">
            <p className="text-sm text-muted-foreground">No tweet available</p>
          </div>
        )}

        <p className="text-sm text-muted-foreground line-clamp-2 mb-3">
          {caseStudy.description}
        </p>

        {/* Tags */}
        {caseStudy.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-3">
            {caseStudy.tags.slice(0, 3).map(tag => (
              <Badge key={tag} variant="outline" className="text-xs">
                #{tag}
              </Badge>
            ))}
            {caseStudy.tags.length > 3 && (
              <Badge variant="outline" className="text-xs">
                +{caseStudy.tags.length - 3}
              </Badge>
            )}
          </div>
        )}
      </CardContent>

      {/* Metrics Footer */}
      {showMetrics && (
        <CardFooter className="pt-3 border-t">
          <div className="grid grid-cols-3 gap-4 w-full text-center">
            {caseStudy.metrics.views && (
              <div>
                <div className="flex items-center justify-center gap-1 text-xs text-muted-foreground mb-1">
                  <Eye className="h-3 w-3" />
                  Views
                </div>
                <div className="text-sm font-semibold">
                  {formatMetric(caseStudy.metrics.views)}
                </div>
              </div>
            )}

            {caseStudy.metrics.likes && (
              <div>
                <div className="flex items-center justify-center gap-1 text-xs text-muted-foreground mb-1">
                  <Heart className="h-3 w-3" />
                  Likes
                </div>
                <div className="text-sm font-semibold">
                  {formatMetric(caseStudy.metrics.likes)}
                </div>
              </div>
            )}

            {caseStudy.metrics.retweets && (
              <div>
                <div className="flex items-center justify-center gap-1 text-xs text-muted-foreground mb-1">
                  <Repeat className="h-3 w-3" />
                  Retweets
                </div>
                <div className="text-sm font-semibold">
                  {formatMetric(caseStudy.metrics.retweets)}
                </div>
              </div>
            )}
          </div>
        </CardFooter>
      )}
    </Card>
  );
}