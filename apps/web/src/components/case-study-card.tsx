import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { TrendingUp, Users, Target } from "lucide-react";
import { motion } from "framer-motion";

interface CaseStudyCardProps {
  title: string;
  category: "media" | "marketing";
  description: string;
  imageUrl?: string;
  metrics: {
    impressions?: number;
    engagement?: number;
    conversions?: number;
  };
}

export function CaseStudyCard({ title, category, description, imageUrl, metrics }: CaseStudyCardProps) {
  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  return (
    <motion.div
      whileHover={{ y: -8, transition: { duration: 0.3 } }}
      whileTap={{ scale: 0.98 }}
      className="h-full"
    >
      <Card className="h-full flex flex-col border-2 border-border/50 hover:border-primary/30 transition-all duration-500 bg-gradient-to-br from-card via-card to-card/90 backdrop-blur-sm shadow-lg hover:shadow-xl hover:shadow-primary/5">
        <CardContent className="flex-1 flex flex-col items-center justify-center p-10">
          <div className="text-center space-y-6">
            <motion.h3 
              className="text-2xl font-semibold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent tracking-wide"
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              {category === "media" ? "BEST MEDIA" : "BEST MARKETING"}
            </motion.h3>
            
            {/* Icon Container */}
            <motion.div 
              className="w-32 h-32 bg-gradient-to-br from-primary/20 to-primary/5 border-2 border-primary/30 rounded-2xl flex items-center justify-center mx-auto relative overflow-hidden"
              whileHover={{ 
                scale: 1.1,
                rotate: 8,
                borderColor: "rgb(4 199 45 / 0.6)",
                boxShadow: "0 20px 40px -12px rgb(4 199 45 / 0.4)"
              }}
              transition={{ type: "spring", stiffness: 400, damping: 10 }}
            >
              <div className="absolute inset-0 bg-gradient-to-br from-primary/10 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300" />
              {category === "media" ? (
                <TrendingUp className="h-12 w-12 text-primary relative z-10" />
              ) : (
                <Target className="h-12 w-12 text-primary relative z-10" />
              )}
            </motion.div>
            
            <motion.div 
              className="space-y-4"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              <p className="text-lg font-semibold">{title}</p>
              <p className="text-sm text-muted-foreground leading-relaxed max-w-xs">
                {description}
              </p>
            </motion.div>

            {/* Key Metrics */}
            {metrics.impressions && (
              <motion.div 
                className="bg-gradient-to-r from-primary/5 to-primary/10 rounded-xl p-4 space-y-2 border border-primary/10"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                whileHover={{ 
                  scale: 1.05,
                  borderColor: "rgb(4 199 45 / 0.3)",
                  boxShadow: "0 8px 25px -8px rgb(4 199 45 / 0.2)"
                }}
              >
                <p className="text-xs text-muted-foreground font-medium tracking-wide">IMPRESSIONS</p>
                <p className="text-2xl font-semibold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                  {formatNumber(metrics.impressions)}
                </p>
              </motion.div>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}