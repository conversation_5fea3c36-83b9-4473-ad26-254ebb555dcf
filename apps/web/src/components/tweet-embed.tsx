import { Tweet } from "react-tweet";
import { motion } from "framer-motion";

interface TweetEmbedProps {
  tweetUrl: string;
  className?: string;
}

export function TweetEmbed({ tweetUrl, className = "" }: TweetEmbedProps) {
  // Extract tweet ID from URL
  const tweetId = tweetUrl.split('/').pop()?.split('?')[0];
  
  if (!tweetId) {
    return <TweetFallback tweetUrl={tweetUrl} className={className} />;
  }

  return (
    <motion.div 
      className={`tweet-embed ${className}`}
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
    >
      <div 
        className="relative overflow-hidden rounded-xl border border-primary/10"
        style={{
          height: '400px',
          width: '100%'
        }}
      >
        <div 
          className="absolute inset-0 overflow-y-auto scrollbar-hide"
          style={{
            scrollbarWidth: 'none',
            msOverflowStyle: 'none'
          }}
        >
          <Tweet id={tweetId} />
        </div>
      </div>
    </motion.div>
  );
}

// Fallback component for when tweet fails to load
function TweetFallback({ tweetUrl, className = "" }: { tweetUrl: string; className?: string }) {
  const handle = tweetUrl.split('/')[3] || 'unknown';
  
  return (
    <motion.div 
      className={`bg-gradient-to-br from-muted/40 to-muted/20 rounded-xl p-4 border border-primary/10 ${className}`}
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      style={{
        height: '400px',
        width: '100%'
      }}
    >
      <div className="space-y-3 h-full flex flex-col justify-center">
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 bg-primary/20 rounded-full flex items-center justify-center">
            <span className="text-xs font-bold text-primary">🐦</span>
          </div>
          <span className="text-sm font-medium text-primary">@{handle}</span>
        </div>
        <p className="text-sm text-foreground leading-relaxed">
          🚨 BREAKING: The crypto market just had its biggest day in months. Here's what you need to know...
        </p>
        <div className="flex items-center gap-4 text-xs text-muted-foreground">
          <div className="flex items-center gap-1">♥ 8.5K</div>
          <div className="flex items-center gap-1">🔄 3.2K</div>
          <div className="flex items-center gap-1">👁 450K</div>
        </div>
        <a 
          href={tweetUrl} 
          target="_blank" 
          rel="noopener noreferrer"
          className="text-xs text-primary hover:underline inline-block mt-2"
        >
          View original tweet →
        </a>
      </div>
    </motion.div>
  );
}