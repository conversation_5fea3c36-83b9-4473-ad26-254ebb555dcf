import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { TrendingUp, Users, Zap } from "lucide-react";
import { motion } from "framer-motion";

interface FeaturedShowcaseProps {
  title: string;
  description?: string;
  imageUrl?: string;
  metrics?: {
    followers?: number;
    engagement?: number;
    growth?: number;
  };
}

export function FeaturedShowcase({ 
  title, 
  description, 
  imageUrl, 
  metrics 
}: FeaturedShowcaseProps) {
  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  return (
    <motion.div
      whileHover={{ y: -8, scale: 1.02, transition: { duration: 0.3 } }}
      whileTap={{ scale: 0.98 }}
      className="h-full"
    >
      <Card className="h-full border-2 border-primary/30 bg-gradient-to-br from-primary/10 via-primary/5 to-primary/15 backdrop-blur-sm shadow-lg hover:shadow-xl hover:shadow-primary/10 hover:border-primary/50 transition-all duration-500">
        <CardContent className="h-full flex flex-col items-center justify-center p-6 sm:p-8 lg:p-10">
          <div className="text-center space-y-6 sm:space-y-8">
            {/* Main Title */}
            <motion.h2 
              className="text-3xl sm:text-4xl font-bold tracking-wider bg-gradient-to-r from-primary via-primary to-primary/70 bg-clip-text text-transparent"
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              {title}
            </motion.h2>
            
            {/* Featured Icon */}
            <motion.div 
              className="w-32 h-32 sm:w-40 sm:h-40 bg-gradient-to-br from-primary/30 to-primary/10 rounded-3xl flex items-center justify-center mx-auto border-2 border-primary/40 relative overflow-hidden"
              whileHover={{ 
                rotate: 12,
                scale: 1.1,
                borderColor: "rgb(4 199 45 / 0.8)",
                boxShadow: "0 25px 50px -12px rgb(4 199 45 / 0.5)"
              }}
              transition={{ 
                type: "spring",
                stiffness: 400,
                damping: 15
              }}
            >
              <div className="absolute inset-0 bg-gradient-to-br from-primary/20 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-500" />
              <Zap className="h-12 w-12 sm:h-16 sm:w-16 text-primary relative z-10" />
            </motion.div>
            
            {description && (
              <motion.p 
                className="text-base text-muted-foreground max-w-sm mx-auto leading-relaxed"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.3 }}
              >
                {description}
              </motion.p>
            )}

            {/* Enhanced Metrics */}
            {metrics && (
              <motion.div 
                className="flex flex-wrap gap-3 sm:gap-4 justify-center"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
              >
                {metrics.followers && (
                  <motion.div 
                    className="bg-gradient-to-r from-card/80 to-card/60 backdrop-blur rounded-xl p-4 border border-primary/20 min-w-[100px]"
                    whileHover={{ 
                      scale: 1.05,
                      borderColor: "rgb(4 199 45 / 0.4)",
                      boxShadow: "0 8px 25px -8px rgb(4 199 45 / 0.3)"
                    }}
                    transition={{ type: "spring", stiffness: 300 }}
                  >
                    <p className="text-xs text-muted-foreground font-medium tracking-wide">FOLLOWERS</p>
                    <p className="text-xl font-semibold text-primary mt-1 bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                      {formatNumber(metrics.followers)}
                    </p>
                  </motion.div>
                )}
                
                {metrics.engagement && (
                  <motion.div 
                    className="bg-gradient-to-r from-card/80 to-card/60 backdrop-blur rounded-xl p-4 border border-primary/20 min-w-[100px]"
                    whileHover={{ 
                      scale: 1.05,
                      borderColor: "rgb(4 199 45 / 0.4)",
                      boxShadow: "0 8px 25px -8px rgb(4 199 45 / 0.3)"
                    }}
                    transition={{ type: "spring", stiffness: 300 }}
                  >
                    <p className="text-xs text-muted-foreground font-medium tracking-wide">ENGAGEMENT</p>
                    <p className="text-xl font-semibold text-primary mt-1 bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                      {metrics.engagement}%
                    </p>
                  </motion.div>
                )}

                {metrics.growth && (
                  <motion.div 
                    className="bg-gradient-to-r from-card/80 to-card/60 backdrop-blur rounded-xl p-4 border border-primary/20 min-w-[100px]"
                    whileHover={{ 
                      scale: 1.05,
                      borderColor: "rgb(4 199 45 / 0.4)",
                      boxShadow: "0 8px 25px -8px rgb(4 199 45 / 0.3)"
                    }}
                    transition={{ type: "spring", stiffness: 300 }}
                  >
                    <p className="text-xs text-muted-foreground font-medium tracking-wide">GROWTH</p>
                    <p className="text-xl font-semibold text-primary mt-1 bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                      +{metrics.growth}%
                    </p>
                  </motion.div>
                )}
              </motion.div>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}