import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useMutation } from 'convex/react';
import { api } from '@backend/api';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  Di<PERSON>Title,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { Save, X, TrendingUp } from 'lucide-react';

interface ElonCounterEditDialogProps {
  isOpen: boolean;
  onClose: () => void;
  currentCount: number;
}

export function ElonCounterEditDialog({
  isOpen,
  onClose,
  currentCount,
}: ElonCounterEditDialogProps) {
  const updateElonCount = useMutation(api.elonInteractions.updateCount);

  const [count, setCount] = useState(currentCount);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setCount(currentCount);
    }
  }, [isOpen, currentCount]);

  const handleSave = async () => {
    try {
      setIsLoading(true);
      await updateElonCount({ count });
      toast.success('Elon counter updated successfully!');
      onClose();
    } catch (error) {
      toast.error(`Failed to update counter: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleIncrement = () => {
    setCount(prev => prev + 1);
  };

  const handleDecrement = () => {
    setCount(prev => Math.max(0, prev - 1));
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-primary" />
            Edit Elon Counter
          </DialogTitle>
        </DialogHeader>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-6 py-4"
        >
          {/* Current vs New */}
          <div className="flex items-center justify-between p-4 bg-muted/50 rounded-lg">
            <div className="text-center">
              <p className="text-sm text-muted-foreground">Current</p>
              <p className="text-2xl font-bold text-primary">{currentCount}</p>
            </div>
            <div className="text-muted-foreground">→</div>
            <div className="text-center">
              <p className="text-sm text-muted-foreground">New</p>
              <p className="text-2xl font-bold text-foreground">{count}</p>
            </div>
          </div>

          {/* Counter Controls */}
          <div className="space-y-4">
            <Label htmlFor="count">Counter Value</Label>
            
            {/* Quick Buttons */}
            <div className="flex items-center gap-2">
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleDecrement}
                disabled={count <= 0}
              >
                -1
              </Button>
              
              <Input
                id="count"
                type="number"
                value={count}
                onChange={(e) => setCount(Math.max(0, parseInt(e.target.value) || 0))}
                className="text-center text-lg font-semibold"
                min="0"
              />
              
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleIncrement}
              >
                +1
              </Button>
            </div>

            {/* Quick Presets */}
            <div className="flex gap-2 flex-wrap">
              {[5, 10, 15, 20, 25, 50].map((preset) => (
                <Button
                  key={preset}
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => setCount(preset)}
                  className="text-xs"
                >
                  Set {preset}
                </Button>
              ))}
            </div>
          </div>

          {/* Info */}
          <div className="text-xs text-muted-foreground bg-blue-50 dark:bg-blue-950/20 p-3 rounded-lg">
            <p className="font-medium mb-1">💡 Counter Tips:</p>
            <ul className="space-y-1">
              <li>• This represents total Elon interactions</li>
              <li>• Changes update immediately on the homepage</li>
              <li>• Use presets for common milestones</li>
            </ul>
          </div>
        </motion.div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isLoading}
          >
            <X className="h-4 w-4 mr-2" />
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            disabled={isLoading || count === currentCount}
          >
            <Save className="h-4 w-4 mr-2" />
            {isLoading ? 'Updating...' : 'Update Counter'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}