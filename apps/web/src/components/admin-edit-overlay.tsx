import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { 
  Edit, 
  Plus, 
  Trash2, 
  Settings, 
  Save, 
  X,
  GripVertical 
} from 'lucide-react';

interface AdminEditOverlayProps {
  children: React.ReactNode;
  isAdmin: boolean;
  onEdit?: () => void;
  onDelete?: () => void;
  onAdd?: () => void;
  onSettings?: () => void;
  canEdit?: boolean;
  canDelete?: boolean;
  canAdd?: boolean;
  canSettings?: boolean;
  isDraggable?: boolean;
  className?: string;
  editLabel?: string;
  deleteLabel?: string;
  addLabel?: string;
}

export function AdminEditOverlay({
  children,
  isAdmin,
  onEdit,
  onDelete,
  onAdd,
  onSettings,
  canEdit = true,
  canDelete = false,
  canAdd = false,
  canSettings = false,
  isDraggable = false,
  className = '',
  editLabel = 'Edit',
  deleteLabel = 'Delete',
  addLabel = 'Add',
}: AdminEditOverlayProps) {
  const [isHovered, setIsHovered] = useState(false);

  if (!isAdmin) {
    return <>{children}</>;
  }

  return (
    <div
      className={`relative group ${className}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {children}

      <AnimatePresence>
        {isHovered && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="absolute inset-0 bg-black/20 backdrop-blur-[1px] rounded-lg border border-primary/30 pointer-events-none"
          >
            {/* Edit buttons overlay */}
            <div className="absolute top-2 right-2 flex gap-1 pointer-events-auto">
              {isDraggable && (
                <Button
                  size="sm"
                  variant="secondary"
                  className="h-8 w-8 p-0 bg-white/90 hover:bg-white shadow-md"
                  title="Drag to reorder"
                >
                  <GripVertical className="h-3 w-3" />
                </Button>
              )}
              
              {canSettings && onSettings && (
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={onSettings}
                  className="h-8 w-8 p-0 bg-white/90 hover:bg-white shadow-md"
                  title="Settings"
                >
                  <Settings className="h-3 w-3" />
                </Button>
              )}

              {canEdit && onEdit && (
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={onEdit}
                  className="h-8 w-8 p-0 bg-white/90 hover:bg-white shadow-md"
                  title={editLabel}
                >
                  <Edit className="h-3 w-3" />
                </Button>
              )}

              {canAdd && onAdd && (
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={onAdd}
                  className="h-8 w-8 p-0 bg-white/90 hover:bg-white shadow-md"
                  title={addLabel}
                >
                  <Plus className="h-3 w-3" />
                </Button>
              )}

              {canDelete && onDelete && (
                <Button
                  size="sm"
                  variant="destructive"
                  onClick={onDelete}
                  className="h-8 w-8 p-0 bg-red-500/90 hover:bg-red-500 shadow-md"
                  title={deleteLabel}
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              )}
            </div>

            {/* Corner indicator */}
            <div className="absolute bottom-2 left-2 pointer-events-none">
              <div className="bg-primary/80 text-primary-foreground px-2 py-1 rounded text-xs font-medium">
                Admin Mode
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

interface AdminSectionHeaderProps {
  title: string;
  isAdmin: boolean;
  onAdd?: () => void;
  onSettings?: () => void;
  addLabel?: string;
  canAdd?: boolean;
  canSettings?: boolean;
  className?: string;
}

export function AdminSectionHeader({
  title,
  isAdmin,
  onAdd,
  onSettings,
  addLabel = 'Add New',
  canAdd = false,
  canSettings = false,
  className = '',
}: AdminSectionHeaderProps) {
  if (!isAdmin) {
    return null;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      className={`flex items-center justify-between mb-4 p-3 bg-primary/5 border border-primary/20 rounded-lg ${className}`}
    >
      <div className="flex items-center gap-2">
        <Settings className="h-4 w-4 text-primary" />
        <span className="text-sm font-medium text-primary">
          Editing: {title}
        </span>
      </div>

      <div className="flex gap-2">
        {canAdd && onAdd && (
          <Button
            size="sm"
            variant="outline"
            onClick={onAdd}
            className="h-8"
          >
            <Plus className="h-3 w-3 mr-1" />
            {addLabel}
          </Button>
        )}

        {canSettings && onSettings && (
          <Button
            size="sm"
            variant="outline"
            onClick={onSettings}
            className="h-8"
          >
            <Settings className="h-3 w-3 mr-1" />
            Settings
          </Button>
        )}
      </div>
    </motion.div>
  );
}