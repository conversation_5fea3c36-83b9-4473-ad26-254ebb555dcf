import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useMutation } from 'convex/react';
import { api } from '@backend/api';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  Di<PERSON>Title,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';
import { Save, X, MessageCircle, Repeat, AtSign } from 'lucide-react';

interface ElonInteraction {
  _id: string;
  type: 'retweet' | 'mention' | 'reply';
  tweetId: string;
  content: string;
  timestamp: number;
  sourceAccount: string;
  isActive: boolean;
}

interface ElonInteractionEditDialogProps {
  isOpen: boolean;
  onClose: () => void;
  elonInteraction: ElonInteraction | null;
  isCreating?: boolean;
}

export function ElonInteractionEditDialog({
  isOpen,
  onClose,
  elonInteraction,
  isCreating = false,
}: ElonInteractionEditDialogProps) {
  const updateElonInteraction = useMutation(api.elonInteractions.updateInteraction);
  const createElonInteraction = useMutation(api.elonInteractions.createInteraction);

  const [formData, setFormData] = useState({
    type: 'retweet' as 'retweet' | 'mention' | 'reply',
    tweetId: '',
    content: '',
    timestamp: Date.now(),
    sourceAccount: '',
    isActive: true,
  });

  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (elonInteraction && !isCreating) {
      setFormData({
        type: elonInteraction.type,
        tweetId: elonInteraction.tweetId,
        content: elonInteraction.content,
        timestamp: elonInteraction.timestamp,
        sourceAccount: elonInteraction.sourceAccount,
        isActive: elonInteraction.isActive,
      });
    } else {
      setFormData({
        type: 'retweet',
        tweetId: '',
        content: '',
        timestamp: Date.now(),
        sourceAccount: '',
        isActive: true,
      });
    }
  }, [elonInteraction, isCreating, isOpen]);

  const handleSave = async () => {
    try {
      setIsLoading(true);

      if (isCreating) {
        await createElonInteraction(formData);
        toast.success('Elon interaction created successfully!');
      } else if (elonInteraction) {
        await updateElonInteraction({
          id: elonInteraction._id,
          ...formData,
        });
        toast.success('Elon interaction updated successfully!');
      }

      onClose();
    } catch (error) {
      toast.error(`Failed to ${isCreating ? 'create' : 'update'} interaction: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'retweet': return <Repeat className="h-4 w-4" />;
      case 'mention': return <AtSign className="h-4 w-4" />;
      case 'reply': return <MessageCircle className="h-4 w-4" />;
      default: return <MessageCircle className="h-4 w-4" />;
    }
  };

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp).toISOString().slice(0, 16);
  };

  const parseTimestamp = (dateTimeString: string) => {
    return new Date(dateTimeString).getTime();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {getTypeIcon(formData.type)}
            {isCreating ? 'Create New Elon Interaction' : 'Edit Elon Interaction'}
          </DialogTitle>
        </DialogHeader>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-6 py-4"
        >
          {/* Interaction Type */}
          <div>
            <Label>Interaction Type</Label>
            <Select
              value={formData.type}
              onValueChange={(value) => handleInputChange('type', value)}
            >
              <SelectTrigger>
                <SelectValue>
                  <div className="flex items-center gap-2">
                    {getTypeIcon(formData.type)}
                    <span className="capitalize">{formData.type}</span>
                  </div>
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="retweet">
                  <div className="flex items-center gap-2">
                    <Repeat className="h-4 w-4" />
                    Retweet
                  </div>
                </SelectItem>
                <SelectItem value="mention">
                  <div className="flex items-center gap-2">
                    <AtSign className="h-4 w-4" />
                    Mention
                  </div>
                </SelectItem>
                <SelectItem value="reply">
                  <div className="flex items-center gap-2">
                    <MessageCircle className="h-4 w-4" />
                    Reply
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Source Account */}
          <div>
            <Label htmlFor="sourceAccount">Source Account</Label>
            <Input
              id="sourceAccount"
              value={formData.sourceAccount}
              onChange={(e) => handleInputChange('sourceAccount', e.target.value)}
              placeholder="@username"
            />
          </div>

          {/* Tweet ID */}
          <div>
            <Label htmlFor="tweetId">Tweet ID</Label>
            <Input
              id="tweetId"
              value={formData.tweetId}
              onChange={(e) => handleInputChange('tweetId', e.target.value)}
              placeholder="1234567890123456789"
            />
          </div>

          {/* Content */}
          <div>
            <Label htmlFor="content">Content</Label>
            <Textarea
              id="content"
              value={formData.content}
              onChange={(e) => handleInputChange('content', e.target.value)}
              placeholder="What did Elon say or do?"
              rows={4}
            />
          </div>

          {/* Timestamp */}
          <div>
            <Label htmlFor="timestamp">Date & Time</Label>
            <Input
              id="timestamp"
              type="datetime-local"
              value={formatTimestamp(formData.timestamp)}
              onChange={(e) => handleInputChange('timestamp', parseTimestamp(e.target.value))}
            />
          </div>

          {/* Active Toggle */}
          <div className="flex items-center space-x-2">
            <Switch
              id="isActive"
              checked={formData.isActive}
              onCheckedChange={(checked) => handleInputChange('isActive', checked)}
            />
            <Label htmlFor="isActive">Active (Show in counter)</Label>
          </div>
        </motion.div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isLoading}
          >
            <X className="h-4 w-4 mr-2" />
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            disabled={isLoading}
          >
            <Save className="h-4 w-4 mr-2" />
            {isLoading ? 'Saving...' : (isCreating ? 'Create' : 'Save')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}