import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Heart, Repeat2, Eye, Users } from "lucide-react";
import { TwitterIcon } from "@/components/ui/twitter-icon";
import { TweetEmbed } from "@/components/tweet-embed";
import { motion } from "framer-motion";

interface SocialCardProps {
  account: {
    handle: string;
    displayName: string;
    platform: string;
  };
  metrics: {
    monthlyImpressions: number;
    monthlySpaceListeners?: number;
    bestPost?: {
      content: string;
      likes: number;
      reposts: number;
      views: number;
    };
  };
  tweetUrl?: string;
}

export function SocialCard({ account, metrics, tweetUrl }: SocialCardProps) {
  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  return (
    <motion.div
      whileHover={{ y: -8, transition: { duration: 0.3 } }}
      whileTap={{ scale: 0.98 }}
      className="h-full"
    >
      <Card className="h-full border-2 border-border/50 hover:border-primary/30 transition-all duration-500 bg-gradient-to-br from-card via-card to-card/90 backdrop-blur-sm shadow-lg hover:shadow-xl hover:shadow-primary/5">
        <CardHeader className="pb-6">
          <div className="flex items-center gap-4">
            <motion.div 
              className="w-12 h-12 bg-gradient-to-br from-primary/20 to-primary/5 rounded-xl flex items-center justify-center border border-primary/20 cursor-pointer"
              whileHover={{ scale: 1.1, rotate: 8 }}
              transition={{ type: "spring", stiffness: 400, damping: 10 }}
              onClick={() => window.open(`https://twitter.com/${account.handle.replace('@', '')}`, '_blank')}
            >
              <TwitterIcon size={20} className="text-primary" />
            </motion.div>
            <div className="flex-1">
              <CardTitle 
                className="text-lg font-semibold bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent cursor-pointer hover:opacity-80 transition-opacity"
                onClick={() => window.open(`https://twitter.com/${account.handle.replace('@', '')}`, '_blank')}
              >
                {account.displayName}
              </CardTitle>
              <p 
                className="text-sm text-primary mt-1 cursor-pointer hover:opacity-80 transition-opacity"
                onClick={() => window.open(`https://twitter.com/${account.handle.replace('@', '')}`, '_blank')}
              >
                {account.handle}
              </p>
              <p className="text-xs text-muted-foreground mt-0.5">{account.platform}</p>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-6 sm:space-y-8">
          {/* Latest Best Post Section */}
          <div className="space-y-4">
            <h4 className="text-sm font-semibold text-primary tracking-wide">LATEST BEST POST</h4>
            
            {/* Real Tweet Embed or Fallback */}
            {tweetUrl ? (
              <motion.div
                whileHover={{ 
                  scale: 1.02,
                  boxShadow: "0 8px 25px -8px rgb(4 199 45 / 0.2)"
                }}
                transition={{ duration: 0.3 }}
              >
                <TweetEmbed 
                  tweetUrl={tweetUrl}
                  className="border border-primary/10 rounded-xl overflow-hidden"
                />
              </motion.div>
            ) : (
              <motion.div 
                className="bg-gradient-to-br from-muted/40 to-muted/20 rounded-xl p-4 sm:p-5 space-y-3 sm:space-y-4 border border-primary/10 backdrop-blur-sm"
                whileHover={{ 
                  borderColor: "rgb(4 199 45 / 0.4)",
                  scale: 1.02,
                  boxShadow: "0 8px 25px -8px rgb(4 199 45 / 0.2)"
                }}
                transition={{ duration: 0.3 }}
              >
                {metrics.bestPost && (
                  <>
                    <p className="text-sm leading-relaxed">
                      {metrics.bestPost.content}
                    </p>
                    
                    <div className="flex items-center gap-4 sm:gap-6 text-xs text-muted-foreground flex-wrap">
                      <motion.div 
                        className="flex items-center gap-1.5 hover:text-primary transition-colors duration-200"
                        whileHover={{ scale: 1.1, y: -1 }}
                        transition={{ type: "spring", stiffness: 400 }}
                      >
                        <Heart className="h-3.5 w-3.5" />
                        <span className="font-medium">{formatNumber(metrics.bestPost.likes)}</span>
                      </motion.div>
                      <motion.div 
                        className="flex items-center gap-1.5 hover:text-primary transition-colors duration-200"
                        whileHover={{ scale: 1.1, y: -1 }}
                        transition={{ type: "spring", stiffness: 400 }}
                      >
                        <Repeat2 className="h-3.5 w-3.5" />
                        <span className="font-medium">{formatNumber(metrics.bestPost.reposts)}</span>
                      </motion.div>
                      <motion.div 
                        className="flex items-center gap-1.5 hover:text-primary transition-colors duration-200"
                        whileHover={{ scale: 1.1, y: -1 }}
                        transition={{ type: "spring", stiffness: 400 }}
                      >
                        <Eye className="h-3.5 w-3.5" />
                        <span className="font-medium">{formatNumber(metrics.bestPost.views)}</span>
                      </motion.div>
                    </div>
                  </>
                )}
              </motion.div>
            )}
          </div>

          {/* Metrics */}
          <div className="space-y-4 sm:space-y-6">
            <motion.div
              className="bg-gradient-to-r from-primary/5 to-primary/10 rounded-lg p-4 border border-primary/10"
              whileHover={{ 
                scale: 1.02,
                borderColor: "rgb(4 199 45 / 0.3)",
                boxShadow: "0 4px 20px -4px rgb(4 199 45 / 0.15)"
              }}
              transition={{ type: "spring", stiffness: 300, duration: 0.2 }}
            >
              <p className="text-xs text-muted-foreground font-medium tracking-wide">MONTHLY IMPRESSIONS</p>
              <p className="text-2xl font-semibold text-primary mt-2 bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                {formatNumber(metrics.monthlyImpressions)}
              </p>
            </motion.div>
            
            {metrics.monthlySpaceListeners && (
              <motion.div
                className="bg-gradient-to-r from-primary/5 to-primary/10 rounded-lg p-4 border border-primary/10"
                whileHover={{ 
                  scale: 1.02,
                  borderColor: "rgb(4 199 45 / 0.3)",
                  boxShadow: "0 4px 20px -4px rgb(4 199 45 / 0.15)"
                }}
                transition={{ type: "spring", stiffness: 300, duration: 0.2 }}
              >
                <p className="text-xs text-muted-foreground font-medium tracking-wide">MONTHLY SPACE LISTENERS</p>
                <p className="text-2xl font-semibold text-primary mt-2 bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                  {formatNumber(metrics.monthlySpaceListeners)}
                </p>
              </motion.div>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}