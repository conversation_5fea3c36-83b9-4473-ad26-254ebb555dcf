import React, { useState, useEffect } from 'react';
import { EvervaultCard } from './evervault-card';

interface DotCardProps {
  target?: number;
  duration?: number;
}

export default function DotCard({ target = 7, duration = 2000 }: DotCardProps) {
  const [count, setCount] = useState(0);

  useEffect(() => {
    let start = 0;
    const end = target;
    const range = end - start;
    if (range <= 0) return;
    const increment = Math.ceil(end / (duration / 50));
    const timer = setInterval(() => {
      start += increment;
      if (start >= end) {
        start = end;
        clearInterval(timer);
      }
      setCount(start);
    }, 50);
    return () => clearInterval(timer);
  }, [target, duration]);

  const display = count < 1000 ? count.toString() : `${Math.floor(count / 1000)}k`;

  return (
    <div className="w-full h-full flex flex-col items-center justify-center space-y-6">
      <h1 className="text-2xl sm:text-3xl font-semibold tracking-tight text-primary bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent text-center">
        ELON MUSK INTERACTIONS
      </h1>
      <div className="w-full max-w-[300px] h-[300px]">
        <EvervaultCard 
          text={display} 
          className="border border-primary/20 hover:border-primary/40 transition-colors duration-300 rounded-2xl overflow-hidden" 
        />
      </div>
    </div>
  );
}
