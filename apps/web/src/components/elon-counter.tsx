import { motion } from "framer-motion";
import { Repeat2, MessageCircle, AtSign, Edit, Trash2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import DotCard from "@/components/ui/moving-dot-card";

interface ElonInteraction {
  _id?: string;
  type: "retweet" | "mention" | "reply";
  content: string;
  timestamp: number;
  sourceAccount: string;
}

interface ElonCounterProps {
  count: number;
  recentInteractions: ElonInteraction[];
  isAdmin?: boolean;
  onEditInteraction?: (interaction: ElonInteraction) => void;
  onDeleteInteraction?: (interaction: ElonInteraction) => void;
}

export function ElonCounter({ 
  count, 
  recentInteractions, 
  isAdmin = false, 
  onEditInteraction, 
  onDeleteInteraction 
}: ElonCounterProps) {
  const getIcon = (type: string) => {
    switch (type) {
      case "retweet":
        return <Repeat2 className="h-3 w-3" />;
      case "reply":
        return <MessageCircle className="h-3 w-3" />;
      case "mention":
        return <AtSign className="h-3 w-3" />;
      default:
        return <Repeat2 className="h-3 w-3" />;
    }
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString();
  };

  return (
    <motion.div
      whileHover={{ y: -8, transition: { duration: 0.3 } }}
      whileTap={{ scale: 0.98 }}
      className="h-full"
    >
      <div className="h-full flex flex-col space-y-4">
        {/* Moving Dot Card */}
        <div className="flex-1">
          <DotCard 
            target={count} 
            duration={2000} 
          />
        </div>

        {/* Recent Activity */}
        {recentInteractions.length > 0 && (
          <motion.div 
            className="space-y-3 px-4 pb-4"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <p className="text-xs font-medium text-primary tracking-wide">RECENT ACTIVITY</p>
            <div className="space-y-2">
              {recentInteractions.slice(0, 2).map((interaction, index) => (
                <motion.div 
                  key={interaction._id || index} 
                  className="bg-gradient-to-r from-muted/40 to-muted/20 rounded-lg p-3 border border-primary/10 backdrop-blur-sm group relative"
                  whileHover={{ 
                    scale: 1.02,
                    borderColor: "rgb(4 199 45 / 0.3)",
                    boxShadow: "0 4px 20px -4px rgb(4 199 45 / 0.15)"
                  }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2 text-xs">
                      <div className="text-primary">
                        {getIcon(interaction.type)}
                      </div>
                      <span className="font-medium text-foreground capitalize">
                        {interaction.type}
                      </span>
                      <span className="text-muted-foreground">
                        • {formatDate(interaction.timestamp)}
                      </span>
                    </div>
                    
                    {/* Admin Controls */}
                    {isAdmin && (
                      <div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                        {onEditInteraction && (
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => onEditInteraction(interaction)}
                            className="h-6 w-6 p-0 hover:bg-primary/20"
                          >
                            <Edit className="h-3 w-3" />
                          </Button>
                        )}
                        {onDeleteInteraction && (
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => onDeleteInteraction(interaction)}
                            className="h-6 w-6 p-0 hover:bg-destructive/20 text-destructive"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        )}
                      </div>
                    )}
                  </div>
                  
                  {/* Interaction Content */}
                  {interaction.content && (
                    <p className="text-xs text-muted-foreground mt-2 line-clamp-2">
                      {interaction.content}
                    </p>
                  )}
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}
      </div>
    </motion.div>
  );
}