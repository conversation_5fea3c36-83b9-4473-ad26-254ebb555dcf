import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  X, 
  Save, 
  Hash, 
  User, 
  ExternalLink, 
  Star,
  Eye,
  Heart,
  Repeat,
  TrendingUp,
  Target,
  BarChart3,
  Link as LinkIcon,
  Check,
  AlertCircle
} from 'lucide-react';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { TweetEmbed } from '@/components/tweet-embed';

interface EnhancedCaseStudyFormProps {
  initialData?: any;
  onSubmit: (data: any) => Promise<void>;
  onCancel: () => void;
  isEditing?: boolean;
}

export function EnhancedCaseStudyForm({ 
  initialData, 
  onSubmit, 
  onCancel, 
  isEditing = false 
}: EnhancedCaseStudyFormProps) {
  const [formData, setFormData] = useState({
    // Basic fields
    title: initialData?.title || '',
    category: initialData?.category || 'media' as 'media' | 'marketing',
    description: initialData?.description || '',
    imageUrl: initialData?.imageUrl || '',
    
    // Tweet-specific fields
    tweetUrl: initialData?.tweetUrl || '',
    tweetId: initialData?.tweetId || '',
    accountHandle: initialData?.accountHandle || '',
    tags: initialData?.tags || [] as string[],
    
    // Enhanced metrics
    impressions: initialData?.metrics?.impressions || 0,
    engagement: initialData?.metrics?.engagement || 0,
    conversions: initialData?.metrics?.conversions || 0,
    likes: initialData?.metrics?.likes || 0,
    retweets: initialData?.metrics?.retweets || 0,
    views: initialData?.metrics?.views || 0,
    
    // Additional fields
    featured: initialData?.featured || false,
    displayOrder: initialData?.displayOrder || 1,
  });

  const [newTag, setNewTag] = useState('');
  const [tweetPreview, setTweetPreview] = useState<boolean>(false);
  const [tweetValid, setTweetValid] = useState<boolean | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Extract tweet ID from URL
  useEffect(() => {
    if (formData.tweetUrl) {
      const tweetId = extractTweetId(formData.tweetUrl);
      if (tweetId) {
        setFormData(prev => ({ ...prev, tweetId }));
        setTweetValid(true);
        setTweetPreview(true);
      } else {
        setTweetValid(false);
        setTweetPreview(false);
      }
    } else {
      setTweetValid(null);
      setTweetPreview(false);
    }
  }, [formData.tweetUrl]);

  const extractTweetId = (url: string): string | null => {
    const tweetRegex = /twitter\.com\/\w+\/status\/(\d+)|x\.com\/\w+\/status\/(\d+)/;
    const match = url.match(tweetRegex);
    return match ? (match[1] || match[2]) : null;
  };

  const validateAccountHandle = (handle: string): boolean => {
    return /^@?[a-zA-Z0-9_]{1,15}$/.test(handle.replace('@', ''));
  };

  const addTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim().toLowerCase())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim().toLowerCase()]
      }));
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addTag();
    }
  };

  const formatAccountHandle = (handle: string): string => {
    return handle.startsWith('@') ? handle : `@${handle}`;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const submitData = {
        title: formData.title,
        category: formData.category,
        description: formData.description,
        imageUrl: formData.imageUrl || undefined,
        tweetUrl: formData.tweetUrl || undefined,
        tweetId: formData.tweetId || undefined,
        accountHandle: formData.accountHandle ? formatAccountHandle(formData.accountHandle) : undefined,
        tags: formData.tags,
        metrics: {
          impressions: formData.impressions || undefined,
          engagement: formData.engagement || undefined,
          conversions: formData.conversions || undefined,
          likes: formData.likes || undefined,
          retweets: formData.retweets || undefined,
          views: formData.views || undefined,
        },
        featured: formData.featured,
        displayOrder: formData.displayOrder,
      };

      await onSubmit(submitData);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="sticky top-8">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          {isEditing ? 'Edit Case Study' : 'Create New Case Study'}
          <Button variant="ghost" size="sm" onClick={onCancel}>
            <X className="h-4 w-4" />
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium text-muted-foreground">Basic Information</h3>
            
            <div>
              <Label htmlFor="title">Title *</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                placeholder="Viral Crypto Campaign"
                required
              />
            </div>

            <div>
              <Label htmlFor="category">Category *</Label>
              <select
                id="category"
                value={formData.category}
                onChange={(e) => setFormData({ ...formData, category: e.target.value as any })}
                className="w-full px-3 py-2 border rounded-md bg-background"
                required
              >
                <option value="media">Media</option>
                <option value="marketing">Marketing</option>
              </select>
            </div>

            <div>
              <Label htmlFor="description">Description *</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Describe the case study and its impact..."
                rows={3}
                required
              />
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="featured"
                checked={formData.featured}
                onCheckedChange={(checked) => setFormData({ ...formData, featured: checked as boolean })}
              />
              <Label htmlFor="featured" className="flex items-center gap-2">
                <Star className="h-4 w-4" />
                Featured case study
              </Label>
            </div>
          </div>

          {/* Tweet Information */}
          <Collapsible defaultOpen={!!formData.tweetUrl}>
            <CollapsibleTrigger className="flex items-center gap-2 text-sm font-medium text-muted-foreground hover:text-foreground">
              <LinkIcon className="h-4 w-4" />
              Tweet Information
            </CollapsibleTrigger>
            <CollapsibleContent className="space-y-4 mt-4">
              <div>
                <Label htmlFor="tweetUrl" className="flex items-center gap-2">
                  Tweet URL
                  {tweetValid === true && <Check className="h-4 w-4 text-green-500" />}
                  {tweetValid === false && <AlertCircle className="h-4 w-4 text-red-500" />}
                </Label>
                <Input
                  id="tweetUrl"
                  value={formData.tweetUrl}
                  onChange={(e) => setFormData({ ...formData, tweetUrl: e.target.value })}
                  placeholder="https://twitter.com/username/status/*********"
                />
                {tweetValid === false && (
                  <p className="text-xs text-red-500 mt-1">Invalid tweet URL format</p>
                )}
              </div>

              {tweetPreview && formData.tweetUrl && (
                <div className="border rounded-lg p-4 bg-muted/50">
                  <TweetEmbed tweetUrl={formData.tweetUrl} className="max-h-96" />
                </div>
              )}

              <div>
                <Label htmlFor="accountHandle">Account Handle</Label>
                <div className="relative">
                  <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="accountHandle"
                    value={formData.accountHandle}
                    onChange={(e) => setFormData({ ...formData, accountHandle: e.target.value })}
                    placeholder="username (without @)"
                    className="pl-10"
                  />
                </div>
              </div>
            </CollapsibleContent>
          </Collapsible>

          {/* Tags */}
          <Collapsible defaultOpen={formData.tags.length > 0}>
            <CollapsibleTrigger className="flex items-center gap-2 text-sm font-medium text-muted-foreground hover:text-foreground">
              <Hash className="h-4 w-4" />
              Tags ({formData.tags.length})
            </CollapsibleTrigger>
            <CollapsibleContent className="space-y-4 mt-4">
              <div className="flex gap-2">
                <Input
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Add tag (e.g., viral, crypto, engagement)"
                  className="flex-1"
                />
                <Button type="button" onClick={addTag} size="sm">
                  Add
                </Button>
              </div>
              
              {formData.tags.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {formData.tags.map(tag => (
                    <Badge key={tag} variant="secondary" className="gap-1">
                      #{tag}
                      <X 
                        className="h-3 w-3 cursor-pointer" 
                        onClick={() => removeTag(tag)} 
                      />
                    </Badge>
                  ))}
                </div>
              )}
            </CollapsibleContent>
          </Collapsible>

          {/* Metrics */}
          <Collapsible defaultOpen>
            <CollapsibleTrigger className="flex items-center gap-2 text-sm font-medium text-muted-foreground hover:text-foreground">
              <BarChart3 className="h-4 w-4" />
              Metrics
            </CollapsibleTrigger>
            <CollapsibleContent className="space-y-4 mt-4">
              {/* Tweet-specific metrics */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="views" className="flex items-center gap-2">
                    <Eye className="h-4 w-4" />
                    Views
                  </Label>
                  <Input
                    id="views"
                    type="number"
                    value={formData.views}
                    onChange={(e) => setFormData({ ...formData, views: parseInt(e.target.value) || 0 })}
                    placeholder="1250000"
                  />
                </div>
                
                <div>
                  <Label htmlFor="likes" className="flex items-center gap-2">
                    <Heart className="h-4 w-4" />
                    Likes
                  </Label>
                  <Input
                    id="likes"
                    type="number"
                    value={formData.likes}
                    onChange={(e) => setFormData({ ...formData, likes: parseInt(e.target.value) || 0 })}
                    placeholder="25000"
                  />
                </div>
                
                <div>
                  <Label htmlFor="retweets" className="flex items-center gap-2">
                    <Repeat className="h-4 w-4" />
                    Retweets
                  </Label>
                  <Input
                    id="retweets"
                    type="number"
                    value={formData.retweets}
                    onChange={(e) => setFormData({ ...formData, retweets: parseInt(e.target.value) || 0 })}
                    placeholder="5000"
                  />
                </div>
                
                <div>
                  <Label htmlFor="impressions" className="flex items-center gap-2">
                    <TrendingUp className="h-4 w-4" />
                    Impressions
                  </Label>
                  <Input
                    id="impressions"
                    type="number"
                    value={formData.impressions}
                    onChange={(e) => setFormData({ ...formData, impressions: parseInt(e.target.value) || 0 })}
                    placeholder="50000000"
                  />
                </div>
              </div>

              {/* Traditional metrics */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 pt-4 border-t">
                <div>
                  <Label htmlFor="engagement" className="flex items-center gap-2">
                    <BarChart3 className="h-4 w-4" />
                    Engagement (%)
                  </Label>
                  <Input
                    id="engagement"
                    type="number"
                    step="0.1"
                    value={formData.engagement}
                    onChange={(e) => setFormData({ ...formData, engagement: parseFloat(e.target.value) || 0 })}
                    placeholder="8.5"
                  />
                </div>
                
                <div>
                  <Label htmlFor="conversions" className="flex items-center gap-2">
                    <Target className="h-4 w-4" />
                    Conversions
                  </Label>
                  <Input
                    id="conversions"
                    type="number"
                    value={formData.conversions}
                    onChange={(e) => setFormData({ ...formData, conversions: parseInt(e.target.value) || 0 })}
                    placeholder="125000"
                  />
                </div>
              </div>
            </CollapsibleContent>
          </Collapsible>

          {/* Additional Settings */}
          <Collapsible>
            <CollapsibleTrigger className="flex items-center gap-2 text-sm font-medium text-muted-foreground hover:text-foreground">
              Additional Settings
            </CollapsibleTrigger>
            <CollapsibleContent className="space-y-4 mt-4">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="imageUrl">Image URL</Label>
                  <Input
                    id="imageUrl"
                    value={formData.imageUrl}
                    onChange={(e) => setFormData({ ...formData, imageUrl: e.target.value })}
                    placeholder="https://example.com/image.jpg"
                  />
                </div>
                
                <div>
                  <Label htmlFor="displayOrder">Display Order</Label>
                  <Input
                    id="displayOrder"
                    type="number"
                    value={formData.displayOrder}
                    onChange={(e) => setFormData({ ...formData, displayOrder: parseInt(e.target.value) || 1 })}
                    required
                  />
                </div>
              </div>
            </CollapsibleContent>
          </Collapsible>

          <Button type="submit" className="w-full gap-2" disabled={isSubmitting}>
            <Save className="h-4 w-4" />
            {isSubmitting 
              ? 'Saving...' 
              : isEditing 
                ? 'Update Case Study' 
                : 'Create Case Study'
            }
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}