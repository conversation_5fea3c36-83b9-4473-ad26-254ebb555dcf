import { Link } from "@tanstack/react-router";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { ChevronDown, Search } from "lucide-react";
import { UserProfile } from "./user-profile";
import { CommandSearch, useCommandSearch } from "./command-search";

export default function Header() {
  const { isOpen, onOpen, onClose } = useCommandSearch();

  return (
    <div>
      <CommandSearch isOpen={isOpen} onClose={onClose} />
      
      <div className="flex flex-row items-center justify-between px-6 py-4">
        <nav className="flex items-center gap-8">
          <Link
            to="/"
            className="text-xl font-semibold hover:text-primary transition-colors"
          >
            IBC
          </Link>
          
          <div className="flex items-center gap-6">
            <Link
              to="/"
              className="text-sm font-medium hover:text-primary transition-colors"
            >
              Home
            </Link>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="text-sm font-medium">
                  Case Studies
                  <ChevronDown className="ml-1 h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="start">
                <DropdownMenuItem asChild>
                  <Link to="/media-case-studies">Media</Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link to="/case-studies/marketing">Marketing</Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link to="/case-studies/incubation">Incubation</Link>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

          </div>
        </nav>
        
        <div className="flex items-center gap-4">
          {/* Search Button */}
          <Button
            variant="outline"
            size="sm"
            onClick={onOpen}
            className="hidden md:flex items-center gap-2"
          >
            <Search className="h-4 w-4" />
            Search
            <div className="ml-2 flex items-center gap-1">
              <kbd className="px-1.5 py-0.5 text-xs bg-muted rounded border">⌘</kbd>
              <kbd className="px-1.5 py-0.5 text-xs bg-muted rounded border">K</kbd>
            </div>
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={onOpen}
            className="md:hidden"
          >
            <Search className="h-4 w-4" />
          </Button>
          
          <UserProfile />
        </div>
      </div>
      <hr />
    </div>
  );
}
