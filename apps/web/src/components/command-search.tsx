import React, { useState, useEffect, useCallback } from "react";
import { Search, Hash, User, TrendingUp, Calendar } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { useQuery } from "convex/react";
import { api } from "@IBC-casestudies/backend";
import { useRouter } from "@tanstack/react-router";

interface SearchResult {
  _id: string;
  title: string;
  description: string;
  category: "media" | "marketing";
  tags: string[];
  accountHandle?: string;
  metrics: {
    views?: number;
    likes?: number;
    retweets?: number;
    impressions?: number;
  };
  featured: boolean;
  createdAt: number;
}

interface CommandSearchProps {
  isOpen: boolean;
  onClose: () => void;
}

export function CommandSearch({ isOpen, onClose }: CommandSearchProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedIndex, setSelectedIndex] = useState(0);
  const router = useRouter();

  // Search results from Convex
  const searchResults = useQuery(
    api.mediaCaseStudies.searchCaseStudies,
    searchTerm.trim() ? { searchTerm: searchTerm.trim(), limit: 10 } : "skip"
  ) as SearchResult[] | undefined;

  // Recent case studies if no search term
  const recentCases = useQuery(
    api.mediaCaseStudies.getMediaCaseStudies,
    searchTerm.trim() ? "skip" : { limit: 6, sortBy: "date" }
  ) as SearchResult[] | undefined;

  const results = searchTerm.trim() ? (searchResults || []) : (recentCases || []);

  // Reset selected index when results change
  useEffect(() => {
    setSelectedIndex(0);
  }, [results]);

  // Handle keyboard navigation
  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    if (!isOpen) return;

    switch (e.key) {
      case "ArrowDown":
        e.preventDefault();
        setSelectedIndex(prev => Math.min(prev + 1, results.length - 1));
        break;
      case "ArrowUp":
        e.preventDefault();
        setSelectedIndex(prev => Math.max(prev - 1, 0));
        break;
      case "Enter":
        e.preventDefault();
        if (results[selectedIndex]) {
          handleSelectResult(results[selectedIndex]);
        }
        break;
      case "Escape":
        e.preventDefault();
        onClose();
        break;
    }
  }, [isOpen, results, selectedIndex, onClose]);

  useEffect(() => {
    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [handleKeyDown]);

  const handleSelectResult = (result: SearchResult) => {
    // Navigate to media case studies page with filter
    router.navigate({
      to: "/media-case-studies",
      search: { highlight: result._id }
    });
    onClose();
  };

  const formatMetric = (value?: number) => {
    if (!value) return "0";
    if (value >= 1000000) return `${(value / 1000000).toFixed(1)}M`;
    if (value >= 1000) return `${(value / 1000).toFixed(1)}K`;
    return value.toString();
  };

  const getResultIcon = (result: SearchResult) => {
    if (result.featured) return <TrendingUp className="h-4 w-4 text-yellow-500" />;
    if (result.accountHandle) return <User className="h-4 w-4 text-blue-500" />;
    return <Hash className="h-4 w-4 text-gray-500" />;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] p-0">
        <DialogHeader className="px-6 pt-6 pb-4">
          <DialogTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Search Case Studies
          </DialogTitle>
        </DialogHeader>

        <div className="px-6 pb-4">
          <Input
            placeholder="Search case studies, tags, or accounts..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full"
            autoFocus
          />
        </div>

        <div className="max-h-96 overflow-y-auto">
          {results.length > 0 ? (
            <div className="space-y-1 px-2 pb-4">
              {!searchTerm.trim() && (
                <div className="px-4 py-2 text-sm text-muted-foreground border-b">
                  Recent case studies
                </div>
              )}
              
              {results.map((result, index) => (
                <Button
                  key={result._id}
                  variant={index === selectedIndex ? "secondary" : "ghost"}
                  className="w-full justify-start p-4 h-auto text-left"
                  onClick={() => handleSelectResult(result)}
                >
                  <div className="flex items-start gap-3 w-full">
                    {getResultIcon(result)}
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium truncate">{result.title}</h4>
                        <Badge variant="outline" className="text-xs">
                          {result.category}
                        </Badge>
                        {result.featured && (
                          <Badge variant="secondary" className="text-xs">
                            Featured
                          </Badge>
                        )}
                      </div>
                      
                      <p className="text-sm text-muted-foreground line-clamp-1 mb-2">
                        {result.description}
                      </p>
                      
                      <div className="flex items-center gap-4 text-xs text-muted-foreground">
                        {result.accountHandle && (
                          <span>{result.accountHandle}</span>
                        )}
                        
                        {result.metrics.views && (
                          <span>{formatMetric(result.metrics.views)} views</span>
                        )}
                        
                        {result.metrics.likes && (
                          <span>{formatMetric(result.metrics.likes)} likes</span>
                        )}
                        
                        <span className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          {new Date(result.createdAt).toLocaleDateString()}
                        </span>
                      </div>
                      
                      {result.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1 mt-2">
                          {result.tags.slice(0, 3).map(tag => (
                            <Badge key={tag} variant="outline" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                          {result.tags.length > 3 && (
                            <Badge variant="outline" className="text-xs">
                              +{result.tags.length - 3}
                            </Badge>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </Button>
              ))}
            </div>
          ) : searchTerm.trim() ? (
            <div className="px-6 py-8 text-center text-muted-foreground">
              <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>No case studies found for "{searchTerm}"</p>
              <p className="text-sm mt-1">Try searching for tags, account handles, or keywords</p>
            </div>
          ) : (
            <div className="px-6 py-8 text-center text-muted-foreground">
              <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>Start typing to search case studies</p>
            </div>
          )}
        </div>

        <div className="px-6 py-3 border-t bg-muted/50 text-xs text-muted-foreground">
          <div className="flex items-center justify-between">
            <span>Use ↑↓ to navigate, Enter to select, Esc to close</span>
            <div className="flex items-center gap-2">
              <kbd className="px-2 py-1 bg-background rounded border">⌘</kbd>
              <span>+</span>
              <kbd className="px-2 py-1 bg-background rounded border">K</kbd>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

// Hook for global keyboard shortcut
export function useCommandSearch() {
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.metaKey || e.ctrlKey) && e.key === "k") {
        e.preventDefault();
        setIsOpen(true);
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, []);

  return {
    isOpen,
    onOpen: () => setIsOpen(true),
    onClose: () => setIsOpen(false),
  };
}