/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as TodosRouteImport } from './routes/todos'
import { Route as MediaCaseStudiesRouteImport } from './routes/media-case-studies'
import { Route as AdminRouteImport } from './routes/admin'
import { Route as IndexRouteImport } from './routes/index'
import { Route as AdminSocialAccountsRouteImport } from './routes/admin/social-accounts'
import { Route as AdminFeaturedContentRouteImport } from './routes/admin/featured-content'
import { Route as AdminElonCounterRouteImport } from './routes/admin/elon-counter'
import { Route as AdminCaseStudiesRouteImport } from './routes/admin/case-studies'

const TodosRoute = TodosRouteImport.update({
  id: '/todos',
  path: '/todos',
  getParentRoute: () => rootRouteImport,
} as any)
const MediaCaseStudiesRoute = MediaCaseStudiesRouteImport.update({
  id: '/media-case-studies',
  path: '/media-case-studies',
  getParentRoute: () => rootRouteImport,
} as any)
const AdminRoute = AdminRouteImport.update({
  id: '/admin',
  path: '/admin',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const AdminSocialAccountsRoute = AdminSocialAccountsRouteImport.update({
  id: '/social-accounts',
  path: '/social-accounts',
  getParentRoute: () => AdminRoute,
} as any)
const AdminFeaturedContentRoute = AdminFeaturedContentRouteImport.update({
  id: '/featured-content',
  path: '/featured-content',
  getParentRoute: () => AdminRoute,
} as any)
const AdminElonCounterRoute = AdminElonCounterRouteImport.update({
  id: '/elon-counter',
  path: '/elon-counter',
  getParentRoute: () => AdminRoute,
} as any)
const AdminCaseStudiesRoute = AdminCaseStudiesRouteImport.update({
  id: '/case-studies',
  path: '/case-studies',
  getParentRoute: () => AdminRoute,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/admin': typeof AdminRouteWithChildren
  '/media-case-studies': typeof MediaCaseStudiesRoute
  '/todos': typeof TodosRoute
  '/admin/case-studies': typeof AdminCaseStudiesRoute
  '/admin/elon-counter': typeof AdminElonCounterRoute
  '/admin/featured-content': typeof AdminFeaturedContentRoute
  '/admin/social-accounts': typeof AdminSocialAccountsRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/admin': typeof AdminRouteWithChildren
  '/media-case-studies': typeof MediaCaseStudiesRoute
  '/todos': typeof TodosRoute
  '/admin/case-studies': typeof AdminCaseStudiesRoute
  '/admin/elon-counter': typeof AdminElonCounterRoute
  '/admin/featured-content': typeof AdminFeaturedContentRoute
  '/admin/social-accounts': typeof AdminSocialAccountsRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/admin': typeof AdminRouteWithChildren
  '/media-case-studies': typeof MediaCaseStudiesRoute
  '/todos': typeof TodosRoute
  '/admin/case-studies': typeof AdminCaseStudiesRoute
  '/admin/elon-counter': typeof AdminElonCounterRoute
  '/admin/featured-content': typeof AdminFeaturedContentRoute
  '/admin/social-accounts': typeof AdminSocialAccountsRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/admin'
    | '/media-case-studies'
    | '/todos'
    | '/admin/case-studies'
    | '/admin/elon-counter'
    | '/admin/featured-content'
    | '/admin/social-accounts'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/admin'
    | '/media-case-studies'
    | '/todos'
    | '/admin/case-studies'
    | '/admin/elon-counter'
    | '/admin/featured-content'
    | '/admin/social-accounts'
  id:
    | '__root__'
    | '/'
    | '/admin'
    | '/media-case-studies'
    | '/todos'
    | '/admin/case-studies'
    | '/admin/elon-counter'
    | '/admin/featured-content'
    | '/admin/social-accounts'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  AdminRoute: typeof AdminRouteWithChildren
  MediaCaseStudiesRoute: typeof MediaCaseStudiesRoute
  TodosRoute: typeof TodosRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/admin': {
      id: '/admin'
      path: '/admin'
      fullPath: '/admin'
      preLoaderRoute: typeof AdminRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/media-case-studies': {
      id: '/media-case-studies'
      path: '/media-case-studies'
      fullPath: '/media-case-studies'
      preLoaderRoute: typeof MediaCaseStudiesRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/todos': {
      id: '/todos'
      path: '/todos'
      fullPath: '/todos'
      preLoaderRoute: typeof TodosRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/admin/case-studies': {
      id: '/admin/case-studies'
      path: '/case-studies'
      fullPath: '/admin/case-studies'
      preLoaderRoute: typeof AdminCaseStudiesRouteImport
      parentRoute: typeof AdminRoute
    }
    '/admin/elon-counter': {
      id: '/admin/elon-counter'
      path: '/elon-counter'
      fullPath: '/admin/elon-counter'
      preLoaderRoute: typeof AdminElonCounterRouteImport
      parentRoute: typeof AdminRoute
    }
    '/admin/featured-content': {
      id: '/admin/featured-content'
      path: '/featured-content'
      fullPath: '/admin/featured-content'
      preLoaderRoute: typeof AdminFeaturedContentRouteImport
      parentRoute: typeof AdminRoute
    }
    '/admin/social-accounts': {
      id: '/admin/social-accounts'
      path: '/social-accounts'
      fullPath: '/admin/social-accounts'
      preLoaderRoute: typeof AdminSocialAccountsRouteImport
      parentRoute: typeof AdminRoute
    }
  }
}

interface AdminRouteChildren {
  AdminCaseStudiesRoute: typeof AdminCaseStudiesRoute
  AdminElonCounterRoute: typeof AdminElonCounterRoute
  AdminFeaturedContentRoute: typeof AdminFeaturedContentRoute
  AdminSocialAccountsRoute: typeof AdminSocialAccountsRoute
}

const AdminRouteChildren: AdminRouteChildren = {
  AdminCaseStudiesRoute: AdminCaseStudiesRoute,
  AdminElonCounterRoute: AdminElonCounterRoute,
  AdminFeaturedContentRoute: AdminFeaturedContentRoute,
  AdminSocialAccountsRoute: AdminSocialAccountsRoute,
}

const AdminRouteWithChildren = AdminRoute._addFileChildren(AdminRouteChildren)

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  AdminRoute: AdminRouteWithChildren,
  MediaCaseStudiesRoute: MediaCaseStudiesRoute,
  TodosRoute: TodosRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
