import { createFileRoute } from '@tanstack/react-router';
import React, { useState, useEffect, useMemo } from 'react';
import { useQuery } from 'convex/react';
import { api } from '@IBC-casestudies/backend';
import { MediaCaseStudyCard } from '@/components/media-case-study-card';
import { MediaCaseStudiesFilters, type FilterState } from '@/components/media-case-studies-filters';
import { CommandSearch, useCommandSearch } from '@/components/command-search';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';
import { useAdminRole } from '@/hooks/useAdminRole';
import { 
  TrendingUp, 
  Eye, 
  Heart, 
  Repeat, 
  BarChart3,
  Search,
  Plus
} from 'lucide-react';

interface SearchParams {
  highlight?: string;
}

export const Route = createFileRoute('/media-case-studies')({
  component: MediaCaseStudiesPage,
  validateSearch: (search): SearchParams => ({
    highlight: search.highlight as string | undefined,
  }),
});

function MediaCaseStudiesPage() {
  const { highlight } = Route.useSearch();
  const { isOpen: isSearchOpen, onClose: onSearchClose } = useCommandSearch();
  const { isAdmin, adminUser } = useAdminRole();

  const [filters, setFilters] = useState<FilterState>({
    tags: [],
    sortBy: 'performance',
    sortOrder: 'desc',
    featuredOnly: false,
    viewMode: 'grid',
    showMetrics: true,
  });

  // Fetch data
  const caseStudies = useQuery(api.mediaCaseStudies.getMediaCaseStudies, {
    tags: filters.tags.length > 0 ? filters.tags : undefined,
    accountHandle: filters.accountHandle,
    minViews: filters.minViews,
    maxViews: filters.maxViews,
    minLikes: filters.minLikes,
    maxLikes: filters.maxLikes,
    sortBy: filters.sortBy,
    sortOrder: filters.sortOrder,
    featuredOnly: filters.featuredOnly,
  });

  const availableTags = useQuery(api.mediaCaseStudies.getCaseStudyTags, {
    category: 'media',
  });

  const availableAccounts = useQuery(api.mediaCaseStudies.getAccountHandles, {
    category: 'media',
  });

  const metricsSummary = useQuery(api.mediaCaseStudies.getMediaMetricsSummary);

  // Handle sharing
  const handleShare = async (caseStudy: any) => {
    const url = `${window.location.origin}/media-case-studies?highlight=${caseStudy._id}`;
    
    if (navigator.share) {
      try {
        await navigator.share({
          title: `IBC Case Study: ${caseStudy.title}`,
          text: caseStudy.description,
          url,
        });
      } catch (error) {
        // User cancelled or share failed
        await copyToClipboard(url);
      }
    } else {
      await copyToClipboard(url);
    }
  };

  const handleCopyUrl = async (url: string) => {
    await copyToClipboard(url);
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success('Copied to clipboard!');
    } catch (error) {
      toast.error('Failed to copy to clipboard');
    }
  };

  // Format metrics for display
  const formatMetric = (value?: number) => {
    if (!value) return '0';
    if (value >= 1000000) return `${(value / 1000000).toFixed(1)}M`;
    if (value >= 1000) return `${(value / 1000).toFixed(1)}K`;
    return value.toString();
  };

  const isLoading = caseStudies === undefined || availableTags === undefined || availableAccounts === undefined;

  if (isLoading) {
    return (
      <div className="container mx-auto py-8 space-y-8">
        {/* Header Skeleton */}
        <div className="space-y-4">
          <Skeleton className="h-12 w-64" />
          <Skeleton className="h-4 w-96" />
        </div>

        {/* Metrics Skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {Array.from({ length: 4 }).map((_, i) => (
            <Skeleton key={i} className="h-24" />
          ))}
        </div>

        {/* Filters Skeleton */}
        <Skeleton className="h-16 w-full" />

        {/* Grid Skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, i) => (
            <Skeleton key={i} className="h-96" />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-8">
      {/* Command Search */}
      <CommandSearch isOpen={isSearchOpen} onClose={onSearchClose} />

      {/* Header */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-4xl font-bold">Media Case Studies</h1>
            <p className="text-muted-foreground mt-2">
              Explore our best-performing social media content and campaigns
            </p>
          </div>
          
          {isAdmin && (
            <div className="flex items-center gap-4">
              <Button
                variant="outline"
                onClick={() => {/* Navigate to admin form */}}
                className="hidden md:flex"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Case Study
              </Button>
            </div>
          )}
        </div>

        {/* Metrics Summary */}
        {metricsSummary && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-card rounded-lg p-6 border">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <BarChart3 className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Total Cases</p>
                  <p className="text-2xl font-bold">{metricsSummary.totalCases}</p>
                </div>
              </div>
            </div>

            <div className="bg-card rounded-lg p-6 border">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-100 rounded-lg">
                  <Eye className="h-6 w-6 text-green-600" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Total Views</p>
                  <p className="text-2xl font-bold">{formatMetric(metricsSummary.totalViews)}</p>
                </div>
              </div>
            </div>

            <div className="bg-card rounded-lg p-6 border">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-red-100 rounded-lg">
                  <Heart className="h-6 w-6 text-red-600" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Total Likes</p>
                  <p className="text-2xl font-bold">{formatMetric(metricsSummary.totalLikes)}</p>
                </div>
              </div>
            </div>

            <div className="bg-card rounded-lg p-6 border">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <TrendingUp className="h-6 w-6 text-purple-600" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Avg Engagement</p>
                  <p className="text-2xl font-bold">{metricsSummary.avgEngagementRate}%</p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Filters */}
      <MediaCaseStudiesFilters
        filters={filters}
        onFiltersChange={setFilters}
        availableTags={availableTags || []}
        availableAccounts={availableAccounts || []}
        totalResults={caseStudies?.length || 0}
      />

      {/* Results */}
      {caseStudies && caseStudies.length > 0 ? (
        <div className={
          filters.viewMode === 'grid'
            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
            : 'space-y-6'
        }>
          {caseStudies.map((caseStudy) => (
            <MediaCaseStudyCard
              key={caseStudy._id}
              caseStudy={caseStudy}
              viewMode={filters.viewMode}
              showMetrics={filters.showMetrics}
              isHighlighted={highlight === caseStudy._id}
              onShare={handleShare}
              onCopyUrl={handleCopyUrl}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-16">
          <div className="mx-auto w-24 h-24 bg-muted rounded-full flex items-center justify-center mb-6">
            <Search className="h-12 w-12 text-muted-foreground" />
          </div>
          <h3 className="text-lg font-semibold mb-2">No case studies found</h3>
          <p className="text-muted-foreground mb-6">
            Try adjusting your filters or search criteria
          </p>
          <Button
            variant="outline"
            onClick={() => setFilters({
              tags: [],
              sortBy: 'performance',
              sortOrder: 'desc',
              featuredOnly: false,
              viewMode: 'grid',
              showMetrics: true,
            })}
          >
            Clear all filters
          </Button>
        </div>
      )}
    </div>
  );
}