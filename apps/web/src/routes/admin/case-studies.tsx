import { createFileRout<PERSON>, <PERSON> } from "@tanstack/react-router";
import { useQuery, useMutation } from "convex/react";
import { api } from "@backend/api";
import { motion } from "framer-motion";
import { useState } from "react";
import { useAdminRole } from "@/hooks/useAdminRole";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { 
  ArrowLeft,
  Plus,
  Edit,
  Trash2,
  Eye,
  EyeOff,
  Save,
  X,
  ExternalLink,
  Award,
  BarChart3,
  TrendingUp,
  Target,
  Image,
} from "lucide-react";
import { toast } from "sonner";
import { EnhancedCaseStudyForm } from "@/components/enhanced-case-study-form";

export const Route = createFileRoute("/admin/case-studies")({
  component: CaseStudiesManagement,
});

interface CaseStudy {
  _id: string;
  title: string;
  category: "media" | "marketing";
  description: string;
  imageUrl?: string;
  tweetUrl?: string;
  tweetId?: string;
  accountHandle?: string;
  tags: string[];
  metrics: {
    impressions?: number;
    engagement?: number;
    conversions?: number;
    likes?: number;
    retweets?: number;
    views?: number;
  };
  featured: boolean;
  performanceScore?: number;
  isActive: boolean;
  displayOrder: number;
  updatedAt: number;
}

function CaseStudiesManagement() {
  const { isAdmin, adminUser, isLoading: isAdminLoading } = useAdminRole();
  const caseStudies = useQuery(api.caseStudies.getAdminCaseStudies);
  const createCaseStudy = useMutation(api.caseStudies.createCaseStudy);
  const updateCaseStudy = useMutation(api.caseStudies.updateCaseStudy);
  const deleteCaseStudy = useMutation(api.caseStudies.deleteCaseStudy);

  const [editingCaseStudy, setEditingCaseStudy] = useState<CaseStudy | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [formData, setFormData] = useState({
    title: "",
    category: "media" as "media" | "marketing",
    description: "",
    imageUrl: "",
    impressions: 0,
    engagement: 0,
    conversions: 0,
    displayOrder: 1,
  });

  const resetForm = () => {
    setFormData({
      title: "",
      category: "media",
      description: "",
      imageUrl: "",
      impressions: 0,
      engagement: 0,
      conversions: 0,
      displayOrder: 1,
    });
    setEditingCaseStudy(null);
    setShowCreateForm(false);
  };

  const handleCreate = async (data: any) => {
    try {
      await createCaseStudy(data);
      toast.success("Case study created successfully!");
      resetForm();
    } catch (error) {
      toast.error(`Failed to create case study: ${error}`);
    }
  };

  const handleUpdate = async (data: any) => {
    if (!editingCaseStudy) return;
    
    try {
      await updateCaseStudy({
        id: editingCaseStudy._id,
        ...data,
      });
      toast.success("Case study updated successfully!");
      resetForm();
    } catch (error) {
      toast.error(`Failed to update case study: ${error}`);
    }
  };

  const handleEdit = (caseStudy: CaseStudy) => {
    setEditingCaseStudy(caseStudy);
    setFormData({
      title: caseStudy.title,
      category: caseStudy.category,
      description: caseStudy.description,
      imageUrl: caseStudy.imageUrl || "",
      impressions: caseStudy.metrics.impressions || 0,
      engagement: caseStudy.metrics.engagement || 0,
      conversions: caseStudy.metrics.conversions || 0,
      displayOrder: caseStudy.displayOrder,
    });
    setShowCreateForm(false);
  };

  const handleDelete = async (id: string, title: string) => {
    if (!confirm(`Are you sure you want to delete "${title}"?`)) return;
    
    try {
      await deleteCaseStudy({ id });
      toast.success("Case study deleted successfully!");
    } catch (error) {
      toast.error(`Failed to delete case study: ${error}`);
    }
  };

  const handleToggleActive = async (caseStudy: CaseStudy) => {
    try {
      await updateCaseStudy({
        id: caseStudy._id,
        isActive: !caseStudy.isActive,
      });
      toast.success(`Case study ${!caseStudy.isActive ? 'activated' : 'deactivated'}!`);
    } catch (error) {
      toast.error(`Failed to toggle case study status: ${error}`);
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "media": return <BarChart3 className="h-4 w-4" />;
      case "marketing": return <Target className="h-4 w-4" />;
      default: return <Award className="h-4 w-4" />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case "media": return "bg-blue-500/10 text-blue-600 border-blue-500/20";
      case "marketing": return "bg-green-500/10 text-green-600 border-green-500/20";
      default: return "bg-muted";
    }
  };

  // Show loading state while checking admin status
  if (isAdminLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
            <p className="mt-4 text-muted-foreground">Checking permissions...</p>
          </div>
        </div>
      </div>
    );
  }

  // Show access denied if not admin
  if (!isAdmin) {
    return (
      <div className="container mx-auto py-8">
        <div className="text-center py-16">
          <div className="mx-auto w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mb-6">
            <X className="h-12 w-12 text-red-600" />
          </div>
          <h3 className="text-lg font-semibold mb-2">Access Denied</h3>
          <p className="text-muted-foreground mb-6">
            You don't have permission to access this admin panel.
          </p>
          <Link to="/">
            <Button variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Home
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  if (!caseStudies) {
    return <div>Loading...</div>;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      {/* Header */}
      <motion.header 
        className="border-b bg-card/50 backdrop-blur-sm"
        initial={{ y: -100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        <div className="container mx-auto px-4 py-4">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
            <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4">
              <Button variant="ghost" size="sm" asChild>
                <Link to="/admin">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  <span className="hidden sm:inline">Back to Dashboard</span>
                  <span className="sm:hidden">Back</span>
                </Link>
              </Button>
              <div className="flex items-center gap-2">
                <Award className="h-5 w-5 sm:h-6 sm:w-6 text-primary" />
                <h1 className="text-lg sm:text-2xl font-bold">Case Studies</h1>
              </div>
            </div>
            <Button 
              onClick={() => {
                setShowCreateForm(true);
                setEditingCaseStudy(null);
              }}
              className="gap-2 w-full sm:w-auto"
              size="sm"
            >
              <Plus className="h-4 w-4" />
              <span className="hidden sm:inline">Add New Case Study</span>
              <span className="sm:hidden">Add Case Study</span>
            </Button>
          </div>
        </div>
      </motion.header>

      <div className="container mx-auto px-4 py-6 sm:py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8">
          {/* Case Studies List */}
          <motion.div 
            className="lg:col-span-2 space-y-4"
            initial={{ x: -50, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ delay: 0.2, duration: 0.5 }}
          >
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
              <h2 className="text-lg sm:text-xl font-semibold">Case Studies ({caseStudies.length})</h2>
              <Button variant="outline" size="sm" asChild className="w-full sm:w-auto">
                <Link to="/">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  <span className="hidden sm:inline">Preview Live Site</span>
                  <span className="sm:hidden">Preview</span>
                </Link>
              </Button>
            </div>

            {caseStudies.map((caseStudy) => (
              <motion.div
                key={caseStudy._id}
                whileHover={{ scale: 1.01 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <Card className={`border-2 ${!caseStudy.isActive ? 'opacity-60' : ''}`}>
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <Badge 
                            variant="outline" 
                            className={`gap-1 ${getCategoryColor(caseStudy.category)}`}
                          >
                            {getCategoryIcon(caseStudy.category)}
                            {caseStudy.category}
                          </Badge>
                          <span className="text-sm text-muted-foreground">
                            Order: {caseStudy.displayOrder}
                          </span>
                        </div>
                        <h3 className="font-semibold text-lg mb-2">{caseStudy.title}</h3>
                        <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                          {caseStudy.description}
                        </p>
                        
                        {/* Metrics */}
                        <div className="flex gap-4 text-xs text-muted-foreground mb-2">
                          {caseStudy.metrics.impressions && (
                            <div className="flex items-center gap-1">
                              <TrendingUp className="h-3 w-3" />
                              {caseStudy.metrics.impressions.toLocaleString()} impressions
                            </div>
                          )}
                          {caseStudy.metrics.engagement && (
                            <div className="flex items-center gap-1">
                              <BarChart3 className="h-3 w-3" />
                              {caseStudy.metrics.engagement}% engagement
                            </div>
                          )}
                          {caseStudy.metrics.conversions && (
                            <div className="flex items-center gap-1">
                              <Target className="h-3 w-3" />
                              {caseStudy.metrics.conversions.toLocaleString()} conversions
                            </div>
                          )}
                        </div>

                        {caseStudy.imageUrl && (
                          <div className="text-xs text-muted-foreground flex items-center gap-1">
                            <Image className="h-3 w-3" />
                            Has image
                          </div>
                        )}
                      </div>
                      <div className="flex items-center gap-2 ml-4">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleToggleActive(caseStudy)}
                        >
                          {caseStudy.isActive ? (
                            <Eye className="h-4 w-4" />
                          ) : (
                            <EyeOff className="h-4 w-4" />
                          )}
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEdit(caseStudy)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(caseStudy._id, caseStudy.title)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>

          {/* Enhanced Form Panel */}
          <motion.div
            initial={{ x: 50, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ delay: 0.4, duration: 0.5 }}
          >
            {(showCreateForm || editingCaseStudy) && (
              <EnhancedCaseStudyForm
                initialData={editingCaseStudy}
                onSubmit={editingCaseStudy ? handleUpdate : handleCreate}
                onCancel={resetForm}
                isEditing={!!editingCaseStudy}
              />
            )}
          </motion.div>
        </div>
      </div>
    </div>
  );
}