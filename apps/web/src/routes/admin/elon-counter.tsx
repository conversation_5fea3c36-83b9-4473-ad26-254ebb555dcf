import { createFile<PERSON><PERSON><PERSON>, <PERSON> } from "@tanstack/react-router";
import { useQuery, useMutation } from "convex/react";
import { api } from "@backend/api";
import { motion } from "framer-motion";
import { useState } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { 
  ArrowLeft,
  Plus,
  Edit,
  Trash2,
  Eye,
  EyeOff,
  Save,
  X,
  ExternalLink,
  Hash,
  MessageCircle,
  Repeat,
  Reply,
  TrendingUp,
} from "lucide-react";
import { toast } from "sonner";

export const Route = createFileRoute("/admin/elon-counter")({
  component: ElonCounterManagement,
});

interface ElonInteraction {
  _id: string;
  type: "retweet" | "mention" | "reply";
  tweetId: string;
  content: string;
  timestamp: number;
  sourceAccount: string;
  isActive: boolean;
}

function ElonCounterManagement() {
  const elonInteractions = useQuery(api.elonInteractions.getAll);
  const elonCount = useQuery(api.elonInteractions.getCount);
  const createInteraction = useMutation(api.elonInteractions.createInteraction);
  const updateInteraction = useMutation(api.elonInteractions.updateInteraction);
  const deleteInteraction = useMutation(api.elonInteractions.deleteInteraction);
  const updateCount = useMutation(api.elonInteractions.updateCount);

  const [editingInteraction, setEditingInteraction] = useState<ElonInteraction | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newCount, setNewCount] = useState(elonCount || 7);
  const [formData, setFormData] = useState({
    type: "retweet" as "retweet" | "mention" | "reply",
    tweetId: "",
    content: "",
    sourceAccount: "",
  });

  const resetForm = () => {
    setFormData({
      type: "retweet",
      tweetId: "",
      content: "",
      sourceAccount: "",
    });
    setEditingInteraction(null);
    setShowCreateForm(false);
  };

  const handleCreate = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await createInteraction({
        ...formData,
        timestamp: Date.now(),
      });
      toast.success("Elon interaction created successfully!");
      resetForm();
    } catch (error) {
      toast.error(`Failed to create interaction: ${error}`);
    }
  };

  const handleUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingInteraction) return;
    
    try {
      await updateInteraction({
        id: editingInteraction._id,
        ...formData,
      });
      toast.success("Elon interaction updated successfully!");
      resetForm();
    } catch (error) {
      toast.error(`Failed to update interaction: ${error}`);
    }
  };

  const handleEdit = (interaction: ElonInteraction) => {
    setEditingInteraction(interaction);
    setFormData({
      type: interaction.type,
      tweetId: interaction.tweetId,
      content: interaction.content,
      sourceAccount: interaction.sourceAccount,
    });
    setShowCreateForm(false);
  };

  const handleDelete = async (id: string) => {
    if (!confirm("Are you sure you want to delete this interaction?")) return;
    
    try {
      await deleteInteraction({ id });
      toast.success("Elon interaction deleted successfully!");
    } catch (error) {
      toast.error(`Failed to delete interaction: ${error}`);
    }
  };

  const handleToggleActive = async (interaction: ElonInteraction) => {
    try {
      await updateInteraction({
        id: interaction._id,
        isActive: !interaction.isActive,
      });
      toast.success(`Interaction ${!interaction.isActive ? 'activated' : 'deactivated'}!`);
    } catch (error) {
      toast.error(`Failed to toggle interaction status: ${error}`);
    }
  };

  const handleUpdateCount = async () => {
    try {
      await updateCount({ count: newCount });
      toast.success("Elon counter updated successfully!");
    } catch (error) {
      toast.error(`Failed to update counter: ${error}`);
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "retweet": return <Repeat className="h-4 w-4" />;
      case "mention": return <MessageCircle className="h-4 w-4" />;
      case "reply": return <Reply className="h-4 w-4" />;
      default: return <Hash className="h-4 w-4" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case "retweet": return "bg-green-500/10 text-green-600 border-green-500/20";
      case "mention": return "bg-blue-500/10 text-blue-600 border-blue-500/20";
      case "reply": return "bg-purple-500/10 text-purple-600 border-purple-500/20";
      default: return "bg-muted";
    }
  };

  if (!elonInteractions) {
    return <div>Loading...</div>;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      {/* Header */}
      <motion.header 
        className="border-b bg-card/50 backdrop-blur-sm"
        initial={{ y: -100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        <div className="container mx-auto px-4 py-4">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
            <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4">
              <Button variant="ghost" size="sm" asChild>
                <Link to="/admin">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  <span className="hidden sm:inline">Back to Dashboard</span>
                  <span className="sm:hidden">Back</span>
                </Link>
              </Button>
              <div className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 sm:h-6 sm:w-6 text-primary" />
                <h1 className="text-lg sm:text-2xl font-bold">Elon Counter</h1>
              </div>
            </div>
            <Button 
              onClick={() => {
                setShowCreateForm(true);
                setEditingInteraction(null);
              }}
              className="gap-2 w-full sm:w-auto"
              size="sm"
            >
              <Plus className="h-4 w-4" />
              <span className="hidden sm:inline">Add New Interaction</span>
              <span className="sm:hidden">Add Interaction</span>
            </Button>
          </div>
        </div>
      </motion.header>

      <div className="container mx-auto px-4 py-6 sm:py-8">
        {/* Counter Control */}
        <motion.div
          className="mb-6 sm:mb-8"
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.1, duration: 0.5 }}
        >
          <Card className="border-2 border-primary/20">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-base sm:text-lg">
                <Hash className="h-5 w-5 text-primary" />
                Elon Counter
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col sm:flex-row sm:items-end gap-4">
                <div className="flex-1 max-w-xs">
                  <Label htmlFor="count">Current Count</Label>
                  <Input
                    id="count"
                    type="number"
                    value={newCount}
                    onChange={(e) => setNewCount(parseInt(e.target.value) || 0)}
                    className="w-full"
                  />
                </div>
                <div className="flex flex-col sm:flex-row gap-2">
                  <Button onClick={handleUpdateCount} className="w-full sm:w-auto">
                    Update Counter
                  </Button>
                  <Button variant="outline" size="sm" asChild className="w-full sm:w-auto">
                    <Link to="/">
                      <ExternalLink className="h-4 w-4 mr-2" />
                      <span className="hidden sm:inline">Preview Live</span>
                      <span className="sm:hidden">Preview</span>
                    </Link>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8">
          {/* Interactions List */}
          <motion.div 
            className="lg:col-span-2 space-y-4"
            initial={{ x: -50, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ delay: 0.2, duration: 0.5 }}
          >
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold">Elon Interactions ({elonInteractions.length})</h2>
            </div>

            {elonInteractions.map((interaction) => (
              <motion.div
                key={interaction._id}
                whileHover={{ scale: 1.01 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <Card className={`border-2 ${!interaction.isActive ? 'opacity-60' : ''}`}>
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <Badge 
                            variant="outline" 
                            className={`gap-1 ${getTypeColor(interaction.type)}`}
                          >
                            {getTypeIcon(interaction.type)}
                            {interaction.type}
                          </Badge>
                          <span className="text-sm text-muted-foreground">
                            {new Date(interaction.timestamp).toLocaleDateString()}
                          </span>
                        </div>
                        <p className="text-sm text-primary font-medium mb-2">
                          {interaction.sourceAccount}
                        </p>
                        <p className="text-sm mb-2">{interaction.content}</p>
                        <p className="text-xs text-muted-foreground">
                          Tweet ID: {interaction.tweetId}
                        </p>
                      </div>
                      <div className="flex items-center gap-2 ml-4">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleToggleActive(interaction)}
                        >
                          {interaction.isActive ? (
                            <Eye className="h-4 w-4" />
                          ) : (
                            <EyeOff className="h-4 w-4" />
                          )}
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEdit(interaction)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(interaction._id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>

          {/* Form Panel */}
          <motion.div
            initial={{ x: 50, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ delay: 0.4, duration: 0.5 }}
          >
            {(showCreateForm || editingInteraction) && (
              <Card className="sticky top-8">
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    {editingInteraction ? 'Edit Interaction' : 'Create New Interaction'}
                    <Button variant="ghost" size="sm" onClick={resetForm}>
                      <X className="h-4 w-4" />
                    </Button>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <form onSubmit={editingInteraction ? handleUpdate : handleCreate} className="space-y-4">
                    <div>
                      <Label htmlFor="type">Interaction Type</Label>
                      <select
                        id="type"
                        value={formData.type}
                        onChange={(e) => setFormData({ ...formData, type: e.target.value as any })}
                        className="w-full px-3 py-2 border rounded-md"
                        required
                      >
                        <option value="retweet">Retweet</option>
                        <option value="mention">Mention</option>
                        <option value="reply">Reply</option>
                      </select>
                    </div>

                    <div>
                      <Label htmlFor="sourceAccount">Source Account</Label>
                      <Input
                        id="sourceAccount"
                        value={formData.sourceAccount}
                        onChange={(e) => setFormData({ ...formData, sourceAccount: e.target.value })}
                        placeholder="@MarioNawfal"
                        required
                      />
                    </div>

                    <div>
                      <Label htmlFor="tweetId">Tweet ID</Label>
                      <Input
                        id="tweetId"
                        value={formData.tweetId}
                        onChange={(e) => setFormData({ ...formData, tweetId: e.target.value })}
                        placeholder="**********"
                        required
                      />
                    </div>

                    <div>
                      <Label htmlFor="content">Content</Label>
                      <Textarea
                        id="content"
                        value={formData.content}
                        onChange={(e) => setFormData({ ...formData, content: e.target.value })}
                        placeholder="This is exactly what crypto needs right now. Great work!"
                        rows={3}
                        required
                      />
                    </div>

                    <Button type="submit" className="w-full gap-2">
                      <Save className="h-4 w-4" />
                      {editingInteraction ? 'Update Interaction' : 'Create Interaction'}
                    </Button>
                  </form>
                </CardContent>
              </Card>
            )}
          </motion.div>
        </div>
      </div>
    </div>
  );
}