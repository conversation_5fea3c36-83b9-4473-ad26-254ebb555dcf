import { createFileRout<PERSON>, <PERSON> } from "@tanstack/react-router";
import { useQuery, useMutation } from "convex/react";
import { api } from "@backend/api";
import { motion } from "framer-motion";
import { useState, useEffect } from "react";
import React from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { 
  ArrowLeft,
  Save,
  ExternalLink,
  Star,
  TrendingUp,
  Users,
  Percent,
} from "lucide-react";
import { toast } from "sonner";

export const Route = createFileRoute("/admin/featured-content")({
  component: FeaturedContentManagement,
});

function FeaturedContentManagement() {
  const featuredConfig = useQuery(api.homepageConfig.getFeaturedConfig);
  const updateFeaturedConfig = useMutation(api.homepageConfig.updateFeaturedConfig);

  const [formData, setFormData] = useState({
    title: "LUNARCRASH",
    description: "Our flagship crypto education platform reaching millions of users worldwide with cutting-edge market analysis and trading insights.",
    followers: 850000,
    engagement: 15.8,
    growth: 245,
  });

  // Update form data when config loads
  useEffect(() => {
    if (featuredConfig) {
      setFormData({
        title: featuredConfig.settings?.title || "LUNARCRASH",
        description: featuredConfig.settings?.description || "Our flagship crypto education platform reaching millions of users worldwide with cutting-edge market analysis and trading insights.",
        followers: featuredConfig.settings?.metrics?.followers || 850000,
        engagement: featuredConfig.settings?.metrics?.engagement || 15.8,
        growth: featuredConfig.settings?.metrics?.growth || 245,
      });
    }
  }, [featuredConfig]);

  const handleUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await updateFeaturedConfig({
        settings: {
          title: formData.title,
          description: formData.description,
          metrics: {
            followers: formData.followers,
            engagement: formData.engagement,
            growth: formData.growth,
          },
        },
      });
      toast.success("Featured content updated successfully!");
    } catch (error) {
      toast.error(`Failed to update featured content: ${error}`);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      {/* Header */}
      <motion.header 
        className="border-b bg-card/50 backdrop-blur-sm"
        initial={{ y: -100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        <div className="container mx-auto px-4 py-4">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
            <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4">
              <Button variant="ghost" size="sm" asChild>
                <Link to="/admin">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  <span className="hidden sm:inline">Back to Dashboard</span>
                  <span className="sm:hidden">Back</span>
                </Link>
              </Button>
              <div className="flex items-center gap-2">
                <Star className="h-5 w-5 sm:h-6 sm:w-6 text-primary" />
                <h1 className="text-lg sm:text-2xl font-bold">Featured Content</h1>
              </div>
            </div>
            <Button variant="outline" size="sm" asChild className="w-full sm:w-auto">
              <Link to="/">
                <ExternalLink className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Preview Live Site</span>
                <span className="sm:hidden">Preview</span>
              </Link>
            </Button>
          </div>
        </div>
      </motion.header>

      <div className="container mx-auto px-4 py-6 sm:py-8">
        <div className="max-w-2xl mx-auto">
          {/* Current Preview */}
          <motion.div
            className="mb-6 sm:mb-8"
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.1, duration: 0.5 }}
          >
            <Card className="border-2 border-primary/20">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-base sm:text-lg">
                  <Star className="h-5 w-5 text-primary" />
                  Current Featured Content
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <h3 className="text-xl sm:text-2xl font-bold">{formData.title}</h3>
                  <p className="text-sm sm:text-base text-muted-foreground">{formData.description}</p>
                  <div className="grid grid-cols-3 gap-2 sm:gap-4 pt-4 border-t">
                    <div className="text-center">
                      <div className="text-lg sm:text-2xl font-bold text-primary">{formData.followers.toLocaleString()}</div>
                      <div className="text-xs sm:text-sm text-muted-foreground">Followers</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg sm:text-2xl font-bold text-primary">{formData.engagement}%</div>
                      <div className="text-xs sm:text-sm text-muted-foreground">Engagement</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg sm:text-2xl font-bold text-primary">+{formData.growth}%</div>
                      <div className="text-xs sm:text-sm text-muted-foreground">Growth</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Edit Form */}
          <motion.div
            initial={{ y: 40, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.3, duration: 0.5 }}
          >
            <Card>
              <CardHeader>
                <CardTitle>Edit Featured Content</CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleUpdate} className="space-y-6">
                  <div>
                    <Label htmlFor="title">Title</Label>
                    <Input
                      id="title"
                      value={formData.title}
                      onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                      placeholder="LUNARCRASH"
                      required
                    />
                  </div>

                  <div>
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      placeholder="Describe your featured content..."
                      rows={4}
                      required
                    />
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                    <div>
                      <Label htmlFor="followers" className="flex items-center gap-2 text-sm">
                        <Users className="h-4 w-4" />
                        Followers
                      </Label>
                      <Input
                        id="followers"
                        type="number"
                        value={formData.followers}
                        onChange={(e) => setFormData({ ...formData, followers: parseInt(e.target.value) || 0 })}
                        required
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="engagement" className="flex items-center gap-2 text-sm">
                        <Percent className="h-4 w-4" />
                        Engagement (%)
                      </Label>
                      <Input
                        id="engagement"
                        type="number"
                        step="0.1"
                        value={formData.engagement}
                        onChange={(e) => setFormData({ ...formData, engagement: parseFloat(e.target.value) || 0 })}
                        required
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="growth" className="flex items-center gap-2 text-sm">
                        <TrendingUp className="h-4 w-4" />
                        Growth (%)
                      </Label>
                      <Input
                        id="growth"
                        type="number"
                        value={formData.growth}
                        onChange={(e) => setFormData({ ...formData, growth: parseInt(e.target.value) || 0 })}
                        required
                      />
                    </div>
                  </div>

                  <Button type="submit" className="w-full gap-2">
                    <Save className="h-4 w-4" />
                    Update Featured Content
                  </Button>
                </form>
              </CardContent>
            </Card>
          </motion.div>

          {/* Help Text */}
          <motion.div
            className="mt-8"
            initial={{ y: 60, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.5, duration: 0.5 }}
          >
            <Card className="bg-muted/50">
              <CardContent className="pt-6">
                <h4 className="font-semibold mb-2">Tips for Featured Content</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Keep the title short and impactful (preferably one word or acronym)</li>
                  <li>• Description should be 1-2 sentences highlighting key value propositions</li>
                  <li>• Metrics should be recent and impressive to build credibility</li>
                  <li>• Update regularly to keep content fresh and engaging</li>
                </ul>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </div>
  );
}