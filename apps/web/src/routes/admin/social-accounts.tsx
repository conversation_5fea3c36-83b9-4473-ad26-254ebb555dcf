import { createFile<PERSON>out<PERSON>, <PERSON> } from "@tanstack/react-router";
import { useQuery, useMutation } from "convex/react";
import { api } from "@backend/api";
import { motion } from "framer-motion";
import { useState } from "react";
import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  Twitter, 
  Plus, 
  Edit, 
  Trash2, 
  Eye, 
  EyeOff,
  ArrowLeft,
  Save,
  X,
  ExternalLink,
} from "lucide-react";
import { toast } from "sonner";

export const Route = createFileRoute("/admin/social-accounts")({
  component: SocialAccountsManagement,
});

interface SocialAccount {
  _id: string;
  handle: string;
  displayName: string;
  platform: string;
  tweetUrl?: string;
  monthlyImpressions: number;
  monthlySpaceListeners?: number;
  isActive: boolean;
  displayOrder: number;
  updatedAt: number;
}

function SocialAccountsManagement() {
  const socialAccounts = useQuery(api.socialAccounts.getAdminSocialAccounts);
  const createAccount = useMutation(api.socialAccounts.createSocialAccount);
  const updateAccount = useMutation(api.socialAccounts.updateSocialAccount);
  const deleteAccount = useMutation(api.socialAccounts.deleteSocialAccount);

  const [editingAccount, setEditingAccount] = useState<SocialAccount | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [formData, setFormData] = useState({
    handle: "",
    displayName: "",
    platform: "X (Twitter)",
    tweetUrl: "",
    monthlyImpressions: 0,
    monthlySpaceListeners: 0,
    displayOrder: 1,
  });

  const resetForm = () => {
    setFormData({
      handle: "",
      displayName: "",
      platform: "X (Twitter)",
      tweetUrl: "",
      monthlyImpressions: 0,
      monthlySpaceListeners: 0,
      displayOrder: 1,
    });
    setEditingAccount(null);
    setShowCreateForm(false);
  };

  const handleCreate = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await createAccount({
        ...formData,
        monthlySpaceListeners: formData.monthlySpaceListeners || undefined,
        tweetUrl: formData.tweetUrl || undefined,
      });
      toast.success("Social account created successfully!");
      resetForm();
    } catch (error) {
      toast.error(`Failed to create account: ${error}`);
    }
  };

  const handleUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingAccount) return;
    
    try {
      await updateAccount({
        id: editingAccount._id,
        ...formData,
        monthlySpaceListeners: formData.monthlySpaceListeners || undefined,
        tweetUrl: formData.tweetUrl || undefined,
      });
      toast.success("Social account updated successfully!");
      resetForm();
    } catch (error) {
      toast.error(`Failed to update account: ${error}`);
    }
  };

  const handleEdit = (account: SocialAccount) => {
    setEditingAccount(account);
    setFormData({
      handle: account.handle,
      displayName: account.displayName,
      platform: account.platform,
      tweetUrl: account.tweetUrl || "",
      monthlyImpressions: account.monthlyImpressions,
      monthlySpaceListeners: account.monthlySpaceListeners || 0,
      displayOrder: account.displayOrder,
    });
    setShowCreateForm(false);
  };

  const handleDelete = async (id: string, handle: string) => {
    if (!confirm(`Are you sure you want to delete ${handle}?`)) return;
    
    try {
      await deleteAccount({ id });
      toast.success("Social account deleted successfully!");
    } catch (error) {
      toast.error(`Failed to delete account: ${error}`);
    }
  };

  const handleToggleActive = async (account: SocialAccount) => {
    try {
      await updateAccount({
        id: account._id,
        isActive: !account.isActive,
      });
      toast.success(`Account ${!account.isActive ? 'activated' : 'deactivated'}!`);
    } catch (error) {
      toast.error(`Failed to toggle account status: ${error}`);
    }
  };

  if (!socialAccounts) {
    return <div>Loading...</div>;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      {/* Header */}
      <motion.header 
        className="border-b bg-card/50 backdrop-blur-sm"
        initial={{ y: -100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        <div className="container mx-auto px-4 py-4">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
            <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4">
              <Button variant="ghost" size="sm" asChild>
                <Link to="/admin">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  <span className="hidden sm:inline">Back to Dashboard</span>
                  <span className="sm:hidden">Back</span>
                </Link>
              </Button>
              <div className="flex items-center gap-2">
                <Twitter className="h-5 w-5 sm:h-6 sm:w-6 text-primary" />
                <h1 className="text-lg sm:text-2xl font-bold">Social Accounts</h1>
              </div>
            </div>
            <Button 
              onClick={() => {
                setShowCreateForm(true);
                setEditingAccount(null);
              }}
              className="gap-2 w-full sm:w-auto"
              size="sm"
            >
              <Plus className="h-4 w-4" />
              <span className="hidden sm:inline">Add New Account</span>
              <span className="sm:hidden">Add Account</span>
            </Button>
          </div>
        </div>
      </motion.header>

      <div className="container mx-auto px-4 py-6 sm:py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8">
          {/* Accounts List */}
          <motion.div 
            className="lg:col-span-2 space-y-4"
            initial={{ x: -50, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ delay: 0.2, duration: 0.5 }}
          >
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
              <h2 className="text-lg sm:text-xl font-semibold">Social Accounts ({socialAccounts.length})</h2>
              <Button variant="outline" size="sm" asChild className="w-full sm:w-auto">
                <Link to="/">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  <span className="hidden sm:inline">Preview Live Site</span>
                  <span className="sm:hidden">Preview</span>
                </Link>
              </Button>
            </div>

            {socialAccounts.map((account) => (
              <motion.div
                key={account._id}
                whileHover={{ scale: 1.01 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <Card className={`border-2 ${!account.isActive ? 'opacity-60' : ''}`}>
                  <CardContent className="p-4 sm:p-6">
                    <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                      <div className="flex items-start sm:items-center gap-3 sm:gap-4 min-w-0 flex-1">
                        <div className="p-2 sm:p-3 rounded-lg bg-primary/10 flex-shrink-0">
                          <Twitter className="h-5 w-5 sm:h-6 sm:w-6 text-primary" />
                        </div>
                        <div className="min-w-0 flex-1">
                          <h3 className="font-semibold text-base sm:text-lg truncate">{account.displayName}</h3>
                          <p className="text-primary text-sm sm:text-base truncate">{account.handle}</p>
                          <p className="text-xs sm:text-sm text-muted-foreground">{account.platform}</p>
                          <div className="text-xs text-muted-foreground mt-1">
                            <div className="break-words">
                              {account.monthlyImpressions.toLocaleString()} impressions
                              {account.monthlySpaceListeners && (
                                <span className="block sm:inline">
                                  <span className="hidden sm:inline"> • </span>
                                  {account.monthlySpaceListeners.toLocaleString()} listeners
                                </span>
                              )}
                            </div>
                          </div>
                          {account.tweetUrl && (
                            <a 
                              href={account.tweetUrl} 
                              target="_blank" 
                              rel="noopener noreferrer"
                              className="text-xs text-primary hover:underline inline-block mt-1"
                            >
                              View Tweet →
                            </a>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center gap-1 sm:gap-2 flex-shrink-0">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleToggleActive(account)}
                          className="p-2"
                        >
                          {account.isActive ? (
                            <Eye className="h-4 w-4" />
                          ) : (
                            <EyeOff className="h-4 w-4" />
                          )}
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEdit(account)}
                          className="p-2"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(account._id, account.handle)}
                          className="p-2"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>

          {/* Form Panel */}
          <motion.div
            initial={{ x: 50, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ delay: 0.4, duration: 0.5 }}
          >
            {(showCreateForm || editingAccount) && (
              <Card className="lg:sticky lg:top-8">
                <CardHeader>
                  <CardTitle className="flex items-center justify-between text-base sm:text-lg">
                    <span className="truncate pr-2">
                      {editingAccount ? 'Edit Account' : 'Create New Account'}
                    </span>
                    <Button variant="ghost" size="sm" onClick={resetForm} className="flex-shrink-0">
                      <X className="h-4 w-4" />
                    </Button>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <form onSubmit={editingAccount ? handleUpdate : handleCreate} className="space-y-4">
                    <div>
                      <Label htmlFor="handle">Handle</Label>
                      <Input
                        id="handle"
                        value={formData.handle}
                        onChange={(e) => setFormData({ ...formData, handle: e.target.value })}
                        placeholder="@MarioNawfal"
                        required
                      />
                    </div>

                    <div>
                      <Label htmlFor="displayName">Display Name</Label>
                      <Input
                        id="displayName"
                        value={formData.displayName}
                        onChange={(e) => setFormData({ ...formData, displayName: e.target.value })}
                        placeholder="Mario Nawfal"
                        required
                      />
                    </div>

                    <div>
                      <Label htmlFor="platform">Platform</Label>
                      <Input
                        id="platform"
                        value={formData.platform}
                        onChange={(e) => setFormData({ ...formData, platform: e.target.value })}
                        placeholder="X (Twitter)"
                        required
                      />
                    </div>

                    <div>
                      <Label htmlFor="tweetUrl">Tweet URL</Label>
                      <Input
                        id="tweetUrl"
                        value={formData.tweetUrl}
                        onChange={(e) => setFormData({ ...formData, tweetUrl: e.target.value })}
                        placeholder="https://x.com/MarioNawfal/status/..."
                      />
                    </div>

                    <div>
                      <Label htmlFor="monthlyImpressions">Monthly Impressions</Label>
                      <Input
                        id="monthlyImpressions"
                        type="number"
                        value={formData.monthlyImpressions}
                        onChange={(e) => setFormData({ ...formData, monthlyImpressions: parseInt(e.target.value) || 0 })}
                        required
                      />
                    </div>

                    <div>
                      <Label htmlFor="monthlySpaceListeners">Monthly Space Listeners</Label>
                      <Input
                        id="monthlySpaceListeners"
                        type="number"
                        value={formData.monthlySpaceListeners}
                        onChange={(e) => setFormData({ ...formData, monthlySpaceListeners: parseInt(e.target.value) || 0 })}
                        placeholder="Optional"
                      />
                    </div>

                    <div>
                      <Label htmlFor="displayOrder">Display Order</Label>
                      <Input
                        id="displayOrder"
                        type="number"
                        value={formData.displayOrder}
                        onChange={(e) => setFormData({ ...formData, displayOrder: parseInt(e.target.value) || 1 })}
                        required
                      />
                    </div>

                    <Button type="submit" className="w-full gap-2">
                      <Save className="h-4 w-4" />
                      {editingAccount ? 'Update Account' : 'Create Account'}
                    </Button>
                  </form>
                </CardContent>
              </Card>
            )}
          </motion.div>
        </div>
      </div>
    </div>
  );
}