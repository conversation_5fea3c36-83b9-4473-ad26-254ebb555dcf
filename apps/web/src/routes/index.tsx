import { createFileRoute } from "@tanstack/react-router";
import { motion } from "framer-motion";
import { useQuery, useMutation } from "convex/react";
import { api } from "@backend/api";
import { SocialCard } from "@/components/social-card";
import { CaseStudyCard } from "@/components/case-study-card";
import { ElonCounter } from "@/components/elon-counter";
import { FeaturedShowcase } from "@/components/featured-showcase";
import { useAdminRole } from "@/hooks/useAdminRole";
import { Button } from "@/components/ui/button";
import { Edit, Plus, Settings, Trash2 } from "lucide-react";
import { useState } from "react";
import { AdminEditOverlay } from "@/components/admin-edit-overlay";
import { SocialAccountEditDialog } from "@/components/social-account-edit-dialog";
import { ElonInteractionEditDialog } from "@/components/elon-interaction-edit-dialog";
import { ElonCounterEditDialog } from "@/components/elon-counter-edit-dialog";
import { Waves } from "@/components/ui/wave-background";
import { toast } from "sonner";

export const Route = createFileRoute("/")({
  component: HomeComponent,
});

function HomeComponent() {
  // Fetch real data from Convex - ALL HOOKS MUST BE CALLED BEFORE ANY CONDITIONAL RETURNS
  const socialAccounts = useQuery(api.socialAccounts.getAll);
  const caseStudies = useQuery(api.caseStudies.getAll);
  const elonInteractions = useQuery(api.elonInteractions.getRecent);
  const elonCountData = useQuery(api.elonInteractions.getCount);
  
  // Admin functionality
  const { isAdmin } = useAdminRole();
  const [editingSection, setEditingSection] = useState<string | null>(null);
  const [editingId, setEditingId] = useState<string | null>(null);
  
  // Dialog states
  const [socialAccountDialog, setSocialAccountDialog] = useState<{
    isOpen: boolean;
    socialAccount: any | null;
    isCreating: boolean;
  }>({
    isOpen: false,
    socialAccount: null,
    isCreating: false,
  });
  
  const [elonInteractionDialog, setElonInteractionDialog] = useState<{
    isOpen: boolean;
    interaction: any | null;
    isCreating: boolean;
  }>({
    isOpen: false,
    interaction: null,
    isCreating: false,
  });
  
  const [elonCounterEditOpen, setElonCounterEditOpen] = useState(false);
  
  // Admin mutations
  const deleteSocialAccount = useMutation(api.socialAccounts.deleteSocialAccount);
  const deleteElonInteraction = useMutation(api.elonInteractions.deleteInteraction);

  // Admin handlers
  const handleEditSocialAccount = (socialAccount: any) => {
    setSocialAccountDialog({
      isOpen: true,
      socialAccount,
      isCreating: false,
    });
  };

  const handleCreateSocialAccount = () => {
    setSocialAccountDialog({
      isOpen: true,
      socialAccount: null,
      isCreating: true,
    });
  };

  const handleDeleteSocialAccount = async (socialAccount: any) => {
    if (!confirm(`Are you sure you want to delete "${socialAccount.displayName}"?`)) return;
    
    try {
      await deleteSocialAccount({ id: socialAccount._id });
      toast.success('Social account deleted successfully!');
    } catch (error) {
      toast.error(`Failed to delete social account: ${error}`);
    }
  };

  const handleEditElonInteraction = (interaction: any) => {
    setElonInteractionDialog({
      isOpen: true,
      interaction,
      isCreating: false,
    });
  };

  const handleCreateElonInteraction = () => {
    setElonInteractionDialog({
      isOpen: true,
      interaction: null,
      isCreating: true,
    });
  };

  const handleDeleteElonInteraction = async (interaction: any) => {
    if (!confirm(`Are you sure you want to delete this interaction?`)) return;
    
    try {
      await deleteElonInteraction({ id: interaction._id });
      toast.success('Elon interaction deleted successfully!');
    } catch (error) {
      toast.error(`Failed to delete interaction: ${error}`);
    }
  };

  // Loading state
  if (!socialAccounts || !caseStudies || !elonInteractions) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  // Transform Convex data to component format
  const transformedSocialAccounts = socialAccounts.map(account => ({
    account: {
      handle: account.handle,
      displayName: account.displayName,
      platform: account.platform
    },
    metrics: {
      monthlyImpressions: account.monthlyImpressions,
      monthlySpaceListeners: account.monthlySpaceListeners,
      bestPost: {
        content: "🚨 BREAKING: The crypto market just had its biggest day in months. Here's what you need to know...",
        likes: 8500,
        reposts: 3200,
        views: 450000
      }
    },
    tweetUrl: account.tweetUrl
  }));

  // Use real case studies or fallback to mock
  const displayCaseStudies = caseStudies.length > 0 ? caseStudies : [
    {
      title: "Viral Crypto Campaign",
      category: "media" as const,
      description: "Launched a multi-platform campaign that reached 50M+ users and generated massive engagement across Twitter, YouTube, and TikTok.",
      metrics: {
        impressions: ********,
        engagement: 8.5,
        conversions: 125000
      }
    },
    {
      title: "DeFi Protocol Launch", 
      category: "marketing" as const,
      description: "Strategic marketing campaign for a new DeFi protocol that achieved $100M TVL within the first month.",
      metrics: {
        impressions: ********,
        engagement: 12.3,
        conversions: 85000
      }
    }
  ];

  // Transform Elon interactions
  const displayElonInteractions = elonInteractions.length > 0 ? elonInteractions : [
    {
      type: "retweet" as const,
      content: "This is exactly what crypto needs right now. Great work!",
      timestamp: Date.now() - ********,
      sourceAccount: "@MarioNawfal"
    },
    {
      type: "mention" as const,
      content: "Impressive analysis on the current market conditions.", 
      timestamp: Date.now() - *********,
      sourceAccount: "@RoundtableSpace"
    },
    {
      type: "reply" as const,
      content: "Looking forward to more discussions like this.",
      timestamp: Date.now() - *********,
      sourceAccount: "@Crypto_TownHall"
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: "easeOut"
      }
    }
  };

  return (
    <div className="min-h-screen flex flex-col relative" style={{ backgroundColor: 'oklch(1 0 0)' }}>
      {/* Wave Background - Fixed to viewport for consistent background */}
      <div className="fixed inset-0 w-screen h-screen z-0">
        <Waves 
          className="w-full h-full"
          strokeColor="oklch(0.54 0.154 142.5 / 0.1)"
          backgroundColor="oklch(1 0 0)"
          pointerSize={0.8}
        />
      </div>
      {/* Main Content */}
      <motion.div 
        className="flex-1 container mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12 max-w-7xl relative z-10"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* Social Media Grid - Top Section */}
        <div className="space-y-6 sm:space-y-8 mb-12 sm:mb-16">
          {/* Admin Controls for Social Accounts */}
          {isAdmin && (
            <motion.div 
              variants={itemVariants}
              className="flex items-center justify-between p-4 bg-primary/5 border border-primary/20 rounded-lg"
            >
              <div className="flex items-center gap-3">
                <Settings className="h-5 w-5 text-primary" />
                <div>
                  <h3 className="font-semibold text-primary">Social Media Management</h3>
                  <p className="text-sm text-muted-foreground">Manage social media accounts and their content</p>
                </div>
              </div>
              <Button
                onClick={handleCreateSocialAccount}
                variant="outline"
                size="sm"
                className="border-primary/30 hover:bg-primary/10"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Account
              </Button>
            </motion.div>
          )}
          {/* First Row: @MarioNawfal | LUNARCRASH */}
          <motion.div 
            className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 lg:gap-8"
            variants={itemVariants}
          >
            <motion.div variants={itemVariants} className="min-h-[350px] sm:min-h-[400px]">
              {transformedSocialAccounts[0] && (
                <AdminEditOverlay
                  isAdmin={isAdmin}
                  canEdit={true}
                  canDelete={true}
                  onEdit={() => handleEditSocialAccount(socialAccounts[0])}
                  onDelete={() => handleDeleteSocialAccount(socialAccounts[0])}
                  editLabel="Edit Social Account"
                  deleteLabel="Delete Account"
                >
                  <SocialCard {...transformedSocialAccounts[0]} />
                </AdminEditOverlay>
              )}
            </motion.div>
            <motion.div variants={itemVariants} className="min-h-[350px] sm:min-h-[400px]">
              <FeaturedShowcase 
                title="LUNARCRASH"
                description="Our flagship crypto education platform reaching millions of users worldwide with cutting-edge market analysis and trading insights."
                metrics={{
                  followers: 850000,
                  engagement: 15.8,
                  growth: 245
                }}
              />
            </motion.div>
          </motion.div>

          {/* Second Row: @RoundtableSpace | @Crypto_TownHall */}
          <motion.div 
            className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 lg:gap-8"
            variants={itemVariants}
          >
            <motion.div variants={itemVariants} className="min-h-[350px] sm:min-h-[400px]">
              {transformedSocialAccounts[1] && (
                <AdminEditOverlay
                  isAdmin={isAdmin}
                  canEdit={true}
                  canDelete={true}
                  onEdit={() => handleEditSocialAccount(socialAccounts[1])}
                  onDelete={() => handleDeleteSocialAccount(socialAccounts[1])}
                  editLabel="Edit Social Account"
                  deleteLabel="Delete Account"
                >
                  <SocialCard {...transformedSocialAccounts[1]} />
                </AdminEditOverlay>
              )}
            </motion.div>
            <motion.div variants={itemVariants} className="min-h-[350px] sm:min-h-[400px]">
              {transformedSocialAccounts[2] && (
                <AdminEditOverlay
                  isAdmin={isAdmin}
                  canEdit={true}
                  canDelete={true}
                  onEdit={() => handleEditSocialAccount(socialAccounts[2])}
                  onDelete={() => handleDeleteSocialAccount(socialAccounts[2])}
                  editLabel="Edit Social Account"
                  deleteLabel="Delete Account"
                >
                  <SocialCard {...transformedSocialAccounts[2]} />
                </AdminEditOverlay>
              )}
            </motion.div>
          </motion.div>
        </div>

        {/* Case Studies Section */}
        <motion.div className="mb-16 sm:mb-20" variants={itemVariants}>
          {/* Case Studies Header */}
          <motion.div 
            className="text-center mb-12 sm:mb-16"
            variants={itemVariants}
          >
            <h2 className="text-3xl sm:text-4xl font-semibold tracking-tight text-primary bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
              CASE STUDIES
            </h2>
            <div className="w-16 sm:w-24 h-1 bg-gradient-to-r from-primary to-primary/50 mx-auto mt-3 sm:mt-4 rounded-full"></div>
          </motion.div>

          {/* Case Studies Grid */}
          <motion.div 
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8"
            variants={containerVariants}
          >
            {/* Best Media */}
            <motion.div variants={itemVariants} className="min-h-[350px] sm:min-h-[400px]">
              {displayCaseStudies[0] && <CaseStudyCard {...displayCaseStudies[0]} />}
            </motion.div>

            {/* Elon Counter */}
            <motion.div variants={itemVariants} className="min-h-[350px] sm:min-h-[400px] relative">
              {/* Custom Admin Overlay for Elon Counter */}
              {isAdmin && (
                <>
                  {/* Add Interaction Button */}
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={handleCreateElonInteraction}
                    className="absolute top-2 right-2 h-8 w-8 p-0 bg-white/90 hover:bg-white shadow-md z-20"
                    title="Add Interaction"
                  >
                    <Plus className="h-3 w-3" />
                  </Button>
                  
                  {/* Edit Counter Button */}
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={() => setElonCounterEditOpen(true)}
                    className="absolute top-2 left-2 h-8 w-8 p-0 bg-white/90 hover:bg-white shadow-md z-20"
                    title="Edit Counter Value"
                  >
                    <Settings className="h-3 w-3" />
                  </Button>
                </>
              )}
              
              <ElonCounter 
                count={elonCountData || 7}
                recentInteractions={displayElonInteractions}
                isAdmin={isAdmin}
                onEditInteraction={handleEditElonInteraction}
                onDeleteInteraction={handleDeleteElonInteraction}
              />
            </motion.div>

            {/* Best Marketing */}
            <motion.div variants={itemVariants} className="min-h-[350px] sm:min-h-[400px]">
              {displayCaseStudies[1] && <CaseStudyCard {...displayCaseStudies[1]} />}
            </motion.div>
          </motion.div>
        </motion.div>
      </motion.div>

      {/* Footer */}
      <motion.footer 
        className="border-t border-border/50 py-8 sm:py-12 bg-background/80 backdrop-blur-sm relative z-10"
        variants={itemVariants}
        initial="hidden"
        animate="visible"
      >
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl">
          <div className="text-center">
            <p className="text-xl sm:text-2xl font-semibold tracking-wide text-foreground/80">FOOTER</p>
            <div className="w-12 sm:w-16 h-0.5 bg-gradient-to-r from-primary to-primary/50 mx-auto mt-2 sm:mt-3 rounded-full"></div>
          </div>
        </div>
      </motion.footer>

      {/* Admin Dialogs */}
      <SocialAccountEditDialog
        isOpen={socialAccountDialog.isOpen}
        onClose={() => setSocialAccountDialog({ isOpen: false, socialAccount: null, isCreating: false })}
        socialAccount={socialAccountDialog.socialAccount}
        isCreating={socialAccountDialog.isCreating}
      />

      <ElonInteractionEditDialog
        isOpen={elonInteractionDialog.isOpen}
        onClose={() => setElonInteractionDialog({ isOpen: false, interaction: null, isCreating: false })}
        elonInteraction={elonInteractionDialog.interaction}
        isCreating={elonInteractionDialog.isCreating}
      />

      <ElonCounterEditDialog
        isOpen={elonCounterEditOpen}
        onClose={() => setElonCounterEditOpen(false)}
        currentCount={elonCountData || 7}
      />
    </div>
  );
}
