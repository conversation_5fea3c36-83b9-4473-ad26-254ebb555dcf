import { createFileRout<PERSON>, <PERSON>, Outlet, useLocation } from "@tanstack/react-router";
import { useConvexAuth, useQuery, useMutation } from "convex/react";
import { api } from "@backend/api";
import { motion } from "framer-motion";
import { useEffect } from "react";
import { useUser } from "@clerk/clerk-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { 
  Settings, 
  Users, 
  BarChart3, 
  Shield, 
  Home,
  Database,
  Twitter,
  Award,
  Activity,
} from "lucide-react";

export const Route = createFileRoute("/admin")({
  component: AdminDashboard,
});

function AdminDashboard() {
  const { isAuthenticated } = useConvexAuth();
  const { user } = useUser();
  const isAdmin = useQuery(api.admin.isAdmin);
  const adminUser = useQuery(api.admin.getAdminUser);
  const linkAdminAccount = useMutation(api.admin.linkAdminAccount);
  const location = useLocation();
  
  // Check if we're on a child route
  const isOnChildRoute = location.pathname !== '/admin';
  
  // Attempt to link admin account if user is authenticated but not admin
  useEffect(() => {
    if (isAuthenticated && isAdmin === false && adminUser === null && user?.primaryEmailAddress?.emailAddress) {
      // Try to link the account in case user was created by email
      linkAdminAccount({ email: user.primaryEmailAddress.emailAddress }).then((result) => {
        if (result?.success) {
          console.log("Admin account linked successfully");
          // The page will automatically refresh due to the query reactivity
        }
      }).catch((error) => {
        console.log("Account linking attempt:", error.message || "No admin account found with this email");
      });
    }
  }, [isAuthenticated, isAdmin, adminUser, user?.primaryEmailAddress?.emailAddress, linkAdminAccount]);

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Authentication Required</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">
              Please sign in to access the admin dashboard.
            </p>
            <Button asChild>
              <Link to="/">Return to Home</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!isAdmin) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-destructive" />
              Access Denied
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">
              You don't have permission to access the admin dashboard.
            </p>
            <Button asChild>
              <Link to="/">Return to Home</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // If we're on a child route, just render the child
  if (isOnChildRoute) {
    return <Outlet />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      {/* Admin Header */}
      <motion.header 
        className="border-b bg-card/50 backdrop-blur-sm"
        initial={{ y: -100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        <div className="container mx-auto px-4 py-4">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
            <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4">
              <div className="flex items-center gap-2">
                <Shield className="h-5 w-5 sm:h-6 sm:w-6 text-primary" />
                <h1 className="text-xl sm:text-2xl font-bold">Admin Dashboard</h1>
              </div>
              <div className="text-xs sm:text-sm text-muted-foreground">
                Welcome back, {adminUser?.name || adminUser?.email}
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" asChild>
                <Link to="/">
                  <Home className="h-4 w-4 mr-2" />
                  <span className="hidden sm:inline">View Site</span>
                  <span className="sm:hidden">Site</span>
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </motion.header>

      {/* Admin Dashboard Content */}
      <div className="container mx-auto px-4 py-8">
        <motion.div 
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8"
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.2, duration: 0.5 }}
        >
          {/* Quick Stats Cards */}
          <StatsCard
            title="Social Accounts"
            icon={Twitter}
            link="/admin/social-accounts"
            description="Manage Twitter accounts and content"
          />
          <StatsCard
            title="Case Studies"
            icon={Award}
            link="/admin/case-studies"
            description="Manage portfolio case studies"
          />
          <StatsCard
            title="Elon Counter"
            icon={Activity}
            link="/admin/elon-counter"
            description="Manage Elon interactions"
          />
          <StatsCard
            title="Featured Content"
            icon={BarChart3}
            link="/admin/featured-content"
            description="Update LUNARCRASH section"
          />
        </motion.div>

        {/* Management Sections */}
        <motion.div 
          className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 lg:gap-8"
          initial={{ y: 40, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.4, duration: 0.5 }}
        >
          {/* Social Media Management */}
          <Card className="border-2">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Twitter className="h-5 w-5 text-primary" />
                Social Media Management
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm text-muted-foreground">
                Manage Twitter accounts, update tweet embeds, and modify metrics displayed on the homepage.
              </p>
              <div className="space-y-2">
                <Button asChild className="w-full justify-start">
                  <Link to="/admin/social-accounts">
                    <Database className="h-4 w-4 mr-2" />
                    Manage Social Accounts
                  </Link>
                </Button>
                <Button variant="outline" asChild className="w-full justify-start">
                  <Link to="/admin/social-accounts/preview">
                    <Activity className="h-4 w-4 mr-2" />
                    Preview Changes
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Content Management */}
          <Card className="border-2">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Award className="h-5 w-5 text-primary" />
                Content Management
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm text-muted-foreground">
                Update case studies, Elon counter, and other homepage content sections.
              </p>
              <div className="space-y-2">
                <Button asChild className="w-full justify-start">
                  <Link to="/admin/case-studies">
                    <Award className="h-4 w-4 mr-2" />
                    Manage Case Studies
                  </Link>
                </Button>
                <Button variant="outline" asChild className="w-full justify-start">
                  <Link to="/admin/elon-counter">
                    <Activity className="h-4 w-4 mr-2" />
                    Elon Counter Settings
                  </Link>
                </Button>
                <Button variant="outline" asChild className="w-full justify-start">
                  <Link to="/admin/featured-content">
                    <BarChart3 className="h-4 w-4 mr-2" />
                    Featured Content
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Quick Actions */}
        <motion.div 
          className="mt-6 sm:mt-8"
          initial={{ y: 60, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.6, duration: 0.5 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="text-lg sm:text-xl">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
                <Button variant="outline" asChild>
                  <Link to="/admin/seed-data">
                    <Database className="h-4 w-4 mr-2" />
                    Seed Sample Data
                  </Link>
                </Button>
                <Button variant="outline" asChild>
                  <Link to="/admin/audit-log">
                    <Activity className="h-4 w-4 mr-2" />
                    View Audit Log
                  </Link>
                </Button>
                <Button variant="outline" asChild>
                  <Link to="/admin/settings">
                    <Settings className="h-4 w-4 mr-2" />
                    System Settings
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}

function StatsCard({ 
  title, 
  icon: Icon, 
  link, 
  description 
}: {
  title: string;
  icon: any;
  link: string;
  description: string;
}) {
  return (
    <motion.div
      whileHover={{ scale: 1.02, y: -2 }}
      transition={{ type: "spring", stiffness: 300 }}
    >
      <Card className="border-2 hover:border-primary/30 transition-colors cursor-pointer">
        <Link to={link}>
          <CardContent className="p-4 sm:p-6">
            <div className="flex items-center gap-3 sm:gap-4">
              <div className="p-2 sm:p-3 rounded-lg bg-primary/10 flex-shrink-0">
                <Icon className="h-5 w-5 sm:h-6 sm:w-6 text-primary" />
              </div>
              <div className="min-w-0 flex-1">
                <h3 className="font-semibold text-sm sm:text-base truncate">{title}</h3>
                <p className="text-xs sm:text-sm text-muted-foreground line-clamp-2">{description}</p>
              </div>
            </div>
          </CardContent>
        </Link>
      </Card>
    </motion.div>
  );
}