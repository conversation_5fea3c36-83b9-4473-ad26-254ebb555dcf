{"name": "web", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --port=3001", "build": "vite build", "serve": "vite preview", "start": "vite", "check-types": "tsc --noEmit"}, "devDependencies": {"@tanstack/react-router-devtools": "^1.114.27", "@tanstack/router-plugin": "^1.114.27", "@types/node": "^22.13.13", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "postcss": "^8.5.3", "tailwindcss": "^4.0.15", "vite": "^6.2.2"}, "dependencies": {"@IBC-casestudies/backend": "workspace:*", "@clerk/clerk-react": "^5.31.9", "@hookform/resolvers": "^3.10.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.2.5", "@tailwindcss/vite": "^4.0.15", "@tanstack/react-form": "^1.0.5", "@tanstack/react-router": "^1.114.25", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "convex": "^1.23.0", "convex-helpers": "^0.1.92", "framer-motion": "^12.17.0", "lucide-react": "^0.473.0", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-tweet": "^3.2.2", "simplex-noise": "^4.0.3", "sonner": "^1.7.4", "tailwind-merge": "^2.6.0", "tw-animate-css": "^1.2.5", "zod": "^3.25.16"}}