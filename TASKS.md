# TASKS.md - Homepage Implementation Plan

## Project Overview
Building a **social media analytics and case studies platform** focused on crypto/tech marketing with real-time metrics tracking and showcase portfolios.

## Phase 1: Authentication Setup (Clerk + Convex)

### 1.1 Install Authentication Dependencies
```bash
bun add @clerk/nextjs convex-auth
```

### 1.2 Configure Clerk
- [ ] Create Clerk application in dashboard
- [ ] Set up JWT template for Convex in Clerk dashboard
- [ ] Add environment variables:
  - `NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY`
  - `CLERK_SECRET_KEY`
  - `CLERK_FRONTEND_API_URL`

### 1.3 Configure Convex Authentication
- [ ] Create `packages/backend/convex/auth.config.js`
- [ ] Update Convex providers in web app
- [ ] Replace current providers with `<ConvexProviderWithClerk>`
- [ ] Update authentication state management

### 1.4 Update Root Layout
- [ ] Wrap app with `<ClerkProvider>`
- [ ] Update `apps/web/src/routes/__root.tsx` with proper auth providers
- [ ] Add authentication routing guards

## Phase 2: Database Schema Design

### 2.1 Social Media Metrics Schema
```typescript
// packages/backend/convex/schema.ts
socialAccounts: defineTable({
  handle: v.string(),           // @MarioNawfal, @RoundtableSpace, etc.
  platform: v.string(),         // "twitter", "spaces"
  displayName: v.string(),
  isActive: v.boolean(),
  createdAt: v.number(),
}),

metrics: defineTable({
  accountId: v.id("socialAccounts"),
  month: v.string(),            // "2024-11"
  impressions: v.number(),
  spaceListeners: v.optional(v.number()),
  bestPostId: v.optional(v.string()),
  bestPostContent: v.optional(v.string()),
  bestPostMetrics: v.optional(v.object({
    likes: v.number(),
    reposts: v.number(),
    views: v.number(),
  })),
  updatedAt: v.number(),
}),

caseStudies: defineTable({
  title: v.string(),
  category: v.union(v.literal("media"), v.literal("marketing")),
  description: v.string(),
  imageUrl: v.optional(v.string()),
  metrics: v.object({
    impressions: v.optional(v.number()),
    engagement: v.optional(v.number()),
    conversions: v.optional(v.number()),
  }),
  isFeatured: v.boolean(),
  createdAt: v.number(),
}),

elonInteractions: defineTable({
  type: v.union(v.literal("retweet"), v.literal("mention"), v.literal("reply")),
  tweetId: v.string(),
  content: v.string(),
  timestamp: v.number(),
  sourceAccount: v.string(),
}),
```

### 2.2 Convex Functions
- [ ] Create CRUD operations for social accounts
- [ ] Create metrics tracking functions
- [ ] Create case studies management functions
- [ ] Create Elon interactions tracking functions

## Phase 3: Component Architecture

### 3.1 Core UI Components

#### 3.1.1 Social Media Cards
```typescript
// apps/web/src/components/social-card.tsx
interface SocialCardProps {
  account: {
    handle: string;
    displayName: string;
    platform: string;
  };
  metrics: {
    monthlyImpressions: number;
    monthlySpaceListeners?: number;
    bestPost: {
      content: string;
      likes: number;
      reposts: number;
      views: number;
    };
  };
}
```

#### 3.1.2 Case Study Showcase Cards
```typescript
// apps/web/src/components/case-study-card.tsx
interface CaseStudyCardProps {
  title: string;
  category: "media" | "marketing";
  description: string;
  imageUrl?: string;
  metrics: {
    impressions?: number;
    engagement?: number;
    conversions?: number;
  };
}
```

#### 3.1.3 Elon Counter Widget
```typescript
// apps/web/src/components/elon-counter.tsx
interface ElonCounterProps {
  count: number;
  recentInteractions: Array<{
    type: string;
    content: string;
    timestamp: number;
  }>;
}
```

### 3.2 Layout Components
- [ ] `apps/web/src/components/homepage-grid.tsx` - Main grid layout
- [ ] `apps/web/src/components/metrics-dashboard.tsx` - Metrics container
- [ ] `apps/web/src/components/case-studies-section.tsx` - Case studies container

## Phase 4: Homepage Implementation

### 4.1 Update Navigation
- [ ] Add Case Studies dropdown to `apps/web/src/components/header.tsx`
- [ ] Add proper routing for Media, Marketing, Incubation categories
- [ ] Add authentication state to navigation

### 4.2 Create Homepage Route
- [ ] Update `apps/web/src/routes/index.tsx` with new layout
- [ ] Implement responsive grid system following 8pt grid
- [ ] Add loading states and error boundaries

### 4.3 Social Media Dashboard Section
- [ ] **@MarioNawfal Card** - Twitter metrics + best post
- [ ] **LUNARCRASH Featured Section** - Hero showcase area
- [ ] **@RoundtableSpace Card** - Twitter metrics + Space listeners + best post
- [ ] **@Crypto_TownHall Card** - Twitter metrics + Space listeners + best post

### 4.4 Case Studies Section
- [ ] **Best Media Card** - Portfolio showcase
- [ ] **Elon Counter Component** - Real-time counter with animations
- [ ] **Best Marketing Card** - Portfolio showcase

## Phase 5: Real-time Data Integration

### 5.1 Twitter/X API Integration (Future)
- [ ] Set up Twitter API v2 credentials
- [ ] Create data fetching service for posts and metrics
- [ ] Implement rate limiting and caching

### 5.2 Manual Data Management (MVP)
- [ ] Create admin interface for updating metrics
- [ ] Add forms for case study management
- [ ] Create Elon interaction tracking system

### 5.3 Real-time Updates
- [ ] Implement Convex subscriptions for live data
- [ ] Add real-time counter animations
- [ ] Implement optimistic updates

## Phase 6: Styling & Design System

### 6.1 Design System Implementation
- [ ] **Typography**: Limit to 4 font sizes, 2 weights (semibold/regular)
- [ ] **8pt Grid System**: All spacing divisible by 8 or 4
- [ ] **60/30/10 Color Rule**: 60% neutral, 30% complementary, 10% accent
- [ ] **TailwindCSS v4**: Update to latest with new @theme directive

### 6.2 Component Styling
- [ ] Style social media cards with proper spacing
- [ ] Create hover effects and animations
- [ ] Implement responsive breakpoints
- [ ] Add dark mode support (already configured)

### 6.3 Performance Optimization
- [ ] Implement proper loading skeletons
- [ ] Add image optimization
- [ ] Minimize bundle size

## Phase 7: Authentication & Authorization

### 7.1 User Authentication
- [ ] Implement sign-in/sign-up flows
- [ ] Add protected routes for admin features
- [ ] Configure user roles and permissions

### 7.2 Admin Features
- [ ] Dashboard for updating social media metrics
- [ ] Case study management interface
- [ ] Analytics and reporting

## Phase 8: Testing & Deployment

### 8.1 Testing
- [ ] Unit tests for components
- [ ] Integration tests for Convex functions
- [ ] E2E tests for critical user flows

### 8.2 Deployment
- [ ] Configure Vercel deployment
- [ ] Set up production Convex environment
- [ ] Configure environment variables

## Technical Requirements

### Dependencies to Add
```json
{
  "@clerk/nextjs": "^5.x.x",
  "convex-auth": "^0.x.x",
  "lucide-react": "^0.x.x",
  "date-fns": "^3.x.x",
  "react-intersection-observer": "^9.x.x"
}
```

### Environment Variables Needed
```env
# Clerk Authentication
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=
CLERK_SECRET_KEY=
CLERK_FRONTEND_API_URL=

# Convex
CONVEX_DEPLOYMENT=
NEXT_PUBLIC_CONVEX_URL=

# Future: Social Media APIs
TWITTER_API_KEY=
TWITTER_API_SECRET=
TWITTER_BEARER_TOKEN=
```

### Phase 9: Media Case Studies Page Implementation

### 9.1 Enhanced Database Schema for Tweet Showcasing
```typescript
// Enhanced case studies schema with tweet-specific metadata
caseStudies: defineTable({
  title: v.string(),
  category: v.union(v.literal("media"), v.literal("marketing")),
  description: v.string(),
  imageUrl: v.optional(v.string()),
  
  // Enhanced metadata for tweets showcase
  tweetUrl: v.optional(v.string()),           // Original tweet URL
  tweetId: v.optional(v.string()),            // Tweet ID for embedding
  accountHandle: v.optional(v.string()),      // @handle for filtering
  tags: v.array(v.string()),                  // ["crypto", "viral", "engagement"]
  
  metrics: v.object({
    impressions: v.optional(v.number()),
    engagement: v.optional(v.number()),
    conversions: v.optional(v.number()),
    likes: v.optional(v.number()),            // Tweet-specific metrics
    retweets: v.optional(v.number()),
    views: v.optional(v.number()),
  }),
  
  // Filtering and search enhancement
  searchableContent: v.string(),              // Combined title + description + tags for fuzzy search
  featured: v.boolean(),                      // Featured showcase priority
  performanceScore: v.optional(v.number()),   // Calculated score for sorting
  
  isFeatured: v.boolean(),
  createdAt: v.number(),
  updatedAt: v.number(),
}).index("by_category", ["category"])
  .index("by_tags", ["tags"])
  .index("by_account", ["accountHandle"])
  .index("by_performance", ["performanceScore"])
  .searchIndex("search_content", {
    searchField: "searchableContent",
    filterFields: ["category", "featured"]
  });
```

### 9.2 Media Case Studies Page Components

#### 9.2.1 Main Media Case Studies Page
- [ ] Create `/media-case-studies` route
- [ ] Implement masonry/grid layout for tweet showcase
- [ ] Add filter sidebar with tags, accounts, metrics
- [ ] Implement infinite scroll for performance
- [ ] Add view mode toggle (grid/list)

#### 9.2.2 Enhanced Tweet Showcase Cards
```typescript
// apps/web/src/components/media-case-study-card.tsx
interface MediaCaseStudyCardProps {
  caseStudy: {
    title: string;
    tweetUrl?: string;
    tweetId?: string;
    accountHandle?: string;
    tags: string[];
    metrics: TweetMetrics;
    performanceScore?: number;
  };
  viewMode: 'grid' | 'list';
  showMetrics: boolean;
}
```

#### 9.2.3 Advanced Filtering System
- [ ] **Filter by Tags**: Multi-select tag filtering
- [ ] **Filter by Account**: Dropdown of all account handles
- [ ] **Filter by Metrics**: Range sliders for views, likes, engagement
- [ ] **Sort Options**: Performance score, date, engagement, views
- [ ] **View Toggles**: Show/hide metrics, featured only

#### 9.2.4 Fuzzy Search with Cmd/Ctrl+K
```typescript
// apps/web/src/components/command-search.tsx
interface SearchModalProps {
  isOpen: boolean;
  onClose: () => void;
  searchResults: CaseStudy[];
  onSelectResult: (result: CaseStudy) => void;
}
```
- [ ] Implement search modal with keyboard shortcuts
- [ ] Use Convex search index for fuzzy matching
- [ ] Add recent searches and suggested filters
- [ ] Keyboard navigation support

### 9.3 Enhanced Admin Interface for Tweet Management

#### 9.3.1 Tweet Case Study Form Enhancement
- [ ] Add tweet URL validator that extracts tweet ID
- [ ] Auto-populate tweet metadata using tweet ID
- [ ] Tag management with autocomplete
- [ ] Metrics input with performance score calculation
- [ ] Bulk import functionality for multiple tweets

#### 9.3.2 Media-Specific Admin Views
- [ ] Dedicated media case studies table view
- [ ] Quick actions for featuring/unfeaturing
- [ ] Performance analytics dashboard
- [ ] Tag management interface
- [ ] Bulk operations (tag assignment, category changes)

### 9.4 Real-time Features and Performance

#### 9.4.1 Live Updates and Subscriptions
- [ ] Real-time updates when admin adds new case studies
- [ ] Live metrics updates using Convex subscriptions
- [ ] Optimistic updates for filtering and search

#### 9.4.2 Performance Optimizations
- [ ] Lazy loading for tweet embeds
- [ ] Virtual scrolling for large datasets
- [ ] Image optimization and lazy loading
- [ ] Cached search results
- [ ] Debounced search and filter inputs

### 9.5 Advanced UI/UX Features

#### 9.5.1 Interactive Features
- [ ] Share functionality for individual case studies
- [ ] Copy tweet URL/embed code
- [ ] Like/bookmark favorites (future)
- [ ] View analytics overlay

#### 9.5.2 Responsive Design
- [ ] Mobile-optimized card layouts
- [ ] Touch-friendly filtering on mobile
- [ ] Responsive search modal
- [ ] Mobile command palette alternative

### 9.6 Backend Functions Implementation

#### 9.6.1 New Convex Functions
```typescript
// packages/backend/convex/mediaCaseStudies.ts
export const getMediaCaseStudies = query(...)      // Filtered by category="media"
export const searchCaseStudies = query(...)        // Fuzzy search implementation
export const getCaseStudyTags = query(...)         // Get all available tags
export const getAccountHandles = query(...)        // Get all account handles
export const updatePerformanceScore = mutation(...) // Calculate performance scores
export const bulkUpdateTags = mutation(...)        // Bulk tag operations
```

#### 9.6.2 Search and Filter Functions
- [ ] Full-text search across title, description, tags
- [ ] Advanced filtering with multiple criteria
- [ ] Performance score calculation algorithm
- [ ] Tag suggestion and auto-completion

## Future Considerations
- Implement a Pin feature in the Case Studies page and a resharable link with those pinned case studies so people can share their own custom case study page.
- Advanced analytics dashboard for media performance
- A/B testing for different showcase layouts
- Integration with social media APIs for real-time metrics

## Success Metrics
- [ ] Authentication flow working seamlessly
- [ ] All 7 main components rendering correctly
- [ ] Responsive design on mobile/tablet/desktop
- [ ] Real-time data updates functioning
- [ ] Performance: < 3s load time, > 90 Lighthouse score
- [ ] Accessibility: WCAG 2.1 AA compliance

## Priority Order
1. **Phase 1** (Authentication) - Critical foundation
2. **Phase 2** (Database) - Core data structure
3. **Phase 4** (Homepage) - MVP implementation with mock data
4. **Phase 3** (Components) - Polish and refinement
5. **Phase 6** (Styling) - Visual enhancement
6. **Phase 5** (Real-time) - Advanced features
7. **Phase 7** (Admin) - Management features
8. **Phase 8** (Testing/Deploy) - Production readiness

## Notes
- Start with mock data for faster development
- Focus on component reusability
- Prioritize mobile-first responsive design
- Maintain type safety throughout
- Document component APIs for team collaboration