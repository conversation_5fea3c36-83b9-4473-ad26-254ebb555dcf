{"permissions": {"allow": ["mcp__context7__resolve-library-id", "mcp__brave-search__brave_web_search", "mcp__context7__get-library-docs", "WebFetch(domain:clerk.com)", "Bash(bun add:*)", "<PERSON><PERSON>(bun dev)", "Bash(bun dev:setup:*)", "mcp__docker-gateway__browser_navigate", "<PERSON><PERSON>(touch:*)", "Bash(find:*)", "Bash(bunx convex env:*)", "Bash(lsof:*)", "<PERSON><PERSON>(curl:*)", "mcp__docker-gateway__browser_install", "Bash(ls:*)", "Bash(bun install:*)", "mcp__convex__status", "mcp__convex__tables", "mcp__convex__envSet", "Bash(bun list:*)", "mcp__convex__data", "mcp__convex__run", "Bash(git add:*)", "Bash(gh repo create:*)", "Bash(git remote add:*)", "Bash(git push:*)", "Bash(bunx:*)", "mcp__convex__functionSpec", "Bash(bun run:*)", "Bash(grep:*)", "mcp__linear__list_projects", "mcp__linear__update_project", "Bash(npx convex run:*)", "mcp__convex__runOneoffQuery"], "deny": []}}