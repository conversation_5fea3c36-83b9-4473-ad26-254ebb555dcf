{"name": "IBC-casestudies", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "turbo dev", "build": "turbo build", "check-types": "turbo check-types", "dev:native": "turbo -F native dev", "dev:web": "turbo -F web dev", "dev:server": "turbo -F @IBC-casestudies/backend dev", "dev:setup": "turbo -F @IBC-casestudies/backend setup"}, "dependencies": {"@clerk/nextjs": "^6.21.0", "convex": "^1.6.0", "convex-dev": "^0.1.1"}, "devDependencies": {"turbo": "^2.5.4"}, "packageManager": "bun@1.2.15"}