# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a modern TypeScript monorepo built with Better-T-Stack combining React frontend with Convex backend. The architecture follows a turborepo structure with separate packages for frontend (web) and backend (Convex functions).

## Essential Commands

### Development
- `bun install` - Install all dependencies
- `bun dev:setup` - Initialize and configure Convex project (required before first run)
- `bun dev` - Start all applications in development mode
- `bun dev:web` - Start only the web application (port 3001)
- `bun dev:server` - Start only the Convex backend

### Build and Type Checking
- `bun build` - Build all applications using Turborepo
- `bun check-types` - Run TypeScript type checking across all packages

## Architecture

### Monorepo Structure
- `apps/web/` - React frontend with TanStack Router and Vite
- `packages/backend/` - Convex backend functions and schema
- Uses Turborepo for orchestrating builds and development

### Tech Stack
- **Frontend**: React 19 + TanStack Router + Vite + TailwindCSS v4
- **Backend**: Convex (serverless backend with real-time subscriptions)
- **UI**: shadcn/ui components with "new-york" style
- **Styling**: TailwindCSS v4 with CSS variables
- **Forms**: TanStack Form with Zod validation
- **Package Manager**: Bun

### Key Patterns

#### TanStack Router Implementation
- File-based routing in `apps/web/src/routes/`
- Root route (`__root.tsx`) provides layout with Header, ThemeProvider, and Toaster
- Type-safe routing with `RouterAppContext` interface
- Router devtools enabled in development

#### Convex Backend Structure
- Schema defined in `packages/backend/convex/schema.ts`
- Database functions in `packages/backend/convex/` (e.g., `todos.ts`)
- Functions exported as `query` and `mutation` from Convex server
- Type-safe database operations with Convex's `v` validators

#### Component Architecture
- shadcn/ui components in `apps/web/src/components/ui/`
- Custom components in `apps/web/src/components/`
- Theme provider with dark mode support (default: dark)
- Path alias `@/` points to `apps/web/src/`

## Development Workflow

### Adding New Features
1. Backend: Add schema changes to `packages/backend/convex/schema.ts`
2. Backend: Create queries/mutations in `packages/backend/convex/`
3. Frontend: Create new routes in `apps/web/src/routes/`
4. Frontend: Add components to `apps/web/src/components/`

### Convex Integration
- Backend functions are imported from `@IBC-casestudies/backend` workspace package
- Real-time data updates through Convex subscriptions
- Type-safe database operations with generated types

### Styling Conventions
- TailwindCSS v4 with CSS variables for theming
- shadcn/ui "new-york" style configuration
- Components use class-variance-authority for variant styling
- Dark theme as default with next-themes integration

## Important Notes

- Convex setup is required before first development run (`bun dev:setup`)
- Web application runs on port 3001
- TanStackRouterDevtools enabled in development mode
- TypeScript strict mode enforced across all packages